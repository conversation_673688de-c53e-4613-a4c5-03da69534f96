package v2

import (
	"context"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	offeringServiceV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type PricingRuleController struct {
	offeringServiceV2.UnimplementedPricingRuleServiceServer
	serviceHandler           service.ServiceHandler
	discountSettingHandler   service.DiscountSettingHandler
	pricingRuleRecordHandler service.PricingRuleRecordHandler
}

func (p PricingRuleController) UpsertPricingRule(ctx context.Context, request *offeringServiceV2.UpsertPricingRuleRequest) (*offeringServiceV2.UpsertPricingRuleResponse, error) {
	pricingRuleRecordDO := requestToDo(request)

	id, err := p.pricingRuleRecordHandler.Upsert(ctx, pricingRuleRecordDO)
	if err != nil {
		zlog.Error(ctx, "failed to upsert pricing rule", zap.Error(err))
		return nil, err
	}
	pricingRuleDo, err := p.pricingRuleRecordHandler.GetById(ctx, request.GetCompanyId(), id)
	if err != nil {
		zlog.Error(ctx, "failed to get pricing rule", zap.Error(err))
		return nil, err
	}
	return &offeringServiceV2.UpsertPricingRuleResponse{
		PricingRule: doToModel(pricingRuleDo),
	}, nil
}

func (p PricingRuleController) GetPricingRule(ctx context.Context, request *offeringServiceV2.GetPricingRuleRequest) (*offeringServiceV2.GetPricingRuleResponse, error) {
	pricingRuleDo, err := p.pricingRuleRecordHandler.GetById(ctx, request.GetCompanyId(), request.GetId())
	if err != nil {
		zlog.Error(ctx, "failed to get pricing rule", zap.Error(err))
		return nil, err
	}
	return &offeringServiceV2.GetPricingRuleResponse{
		PricingRule: doToModel(pricingRuleDo),
	}, nil
}

func (p PricingRuleController) ListPricingRules(ctx context.Context, request *offeringServiceV2.ListPricingRulesRequest) (*offeringServiceV2.ListPricingRulesResponse, error) {
	whereOpt := &models.PricingRuleRecordWhereOpt{
		CompanyID: request.CompanyId,
	}
	filter := request.Filter
	if filter != nil {
		whereOpt.RuleTypes = filter.RuleTypes
		whereOpt.CareTypes = filter.CareTypes
		whereOpt.IsActive = filter.IsActive
		whereOpt.IDs = filter.Ids
		whereOpt.ExcludedIDs = filter.ExcludeIds
	}

	rules, total, err := p.pricingRuleRecordHandler.ListByPage(ctx, whereOpt, request.Pagination)
	if err != nil {
		zlog.Error(ctx, "failed to list pricing rules", zap.Error(err))
		return nil, err
	}
	return &offeringServiceV2.ListPricingRulesResponse{
		PricingRules: doListToModelList(rules),
		Pagination: &utilsV2.PaginationResponse{
			Total:    total,
			PageSize: *request.Pagination.PageSize,
			PageNum:  *request.Pagination.PageNum,
		},
	}, nil
}

func (p PricingRuleController) CalculatePricingRule(ctx context.Context, request *offeringServiceV2.CalculatePricingRuleRequest) (*offeringServiceV2.CalculatePricingRuleResponse, error) {
	compositeDO, err := p.calculateCompositeDO(ctx, request)
	if err != nil {
		return nil, err
	}
	if compositeDO == nil {
		return &offeringServiceV2.CalculatePricingRuleResponse{}, nil
	}

	result, err := p.pricingRuleRecordHandler.Calculate(ctx, compositeDO)
	if err != nil {
		zlog.Error(ctx, "failed to calculate pricing rule", zap.Error(err))
		return nil, err
	}

	return &offeringServiceV2.CalculatePricingRuleResponse{
		PetDetails: doToDef(result.CalculateResultDOS),
		Formula:    result.Formula,
	}, nil
}

func doToDef(dos []*do.CalculateResultDO) []*offeringModelsV2.PetDetailCalculateResultDef {
	petDetails := make([]*offeringModelsV2.PetDetailCalculateResultDef, 0)
	for _, resultDO := range dos {
		price, _ := resultDO.AdjustedPrice.Float64()
		petDetails = append(petDetails, &offeringModelsV2.PetDetailCalculateResultDef{
			PetId:          resultDO.PetId,
			ServiceId:      resultDO.ServiceId,
			AdjustedPrice:  price,
			ServiceDate:    resultDO.ServiceDate,
			AppliedRuleIds: resultDO.UsedPricingRuleIds,
		})
	}
	return petDetails
}

func (p PricingRuleController) calculateCompositeDO(ctx context.Context, request *offeringServiceV2.CalculatePricingRuleRequest) (*do.CalculateCompositeDO, error) {
	compositeDO := &do.CalculateCompositeDO{
		IsPreview: request.IsPreview,
		CompanyID: request.CompanyId,
	}

	petDetailCalculateDOS := make([]*do.CalculateDO, 0)
	for _, detail := range request.PetDetails {
		if detail.ScopeTypePrice != nil && *detail.ScopeTypePrice != offeringModelsV1.ServiceScopeType_DO_NOT_SAVE {
			continue
		}
		petDetailCalculateDOS = append(petDetailCalculateDOS, &do.CalculateDO{
			PetId:              detail.PetId,
			ServiceId:          detail.ServiceId,
			ServicePrice:       decimal.NewFromFloat(detail.ServicePrice),
			LodgingUnitId:      detail.LodgingUnitId,
			ScopeTypePrice:     detail.ScopeTypePrice,
			ServiceDate:        detail.ServiceDate,
			IsHitPricingRule:   false,
			UsedPricingRuleIds: make([]int64, 0),
		})
	}

	services, _ := p.getServices(ctx, petDetailCalculateDOS)
	compositeDO.CalculateDOS = setServiceItemType(petDetailCalculateDOS, services)

	// get hit pricing rules
	hitRules, err := p.getHitPricingRules(ctx, request.CompanyId, services)
	if err != nil || len(hitRules) == 0 {
		return nil, err
	}

	compositeDO.PricingRuleRecordDOS = hitRules

	// discount setting
	if request.Setting != nil {
		compositeDO.ApplyBestOnly = request.Setting.ApplyBestOnly
		compositeDO.ApplySequence = request.Setting.ApplySequence
	} else {
		discountSetting, err := p.discountSettingHandler.GetByCompanyId(ctx, request.CompanyId)
		if err != nil {
			zlog.Error(ctx, "failed to get discount setting", zap.Error(err))
			return nil, err
		}
		if discountSetting != nil {
			compositeDO.ApplyBestOnly = discountSetting.ApplyBestOnly
			compositeDO.ApplySequence = discountSetting.ApplySequence
		}
	}

	return compositeDO, nil
}

func (p PricingRuleController) DeletePricingRule(ctx context.Context, request *offeringServiceV2.DeletePricingRuleRequest) (*offeringServiceV2.DeletePricingRuleResponse, error) {
	err := p.pricingRuleRecordHandler.Delete(ctx, request.CompanyId, request.Id, request.StaffId)
	if err != nil {
		zlog.Error(ctx, "failed to delete pricing rule", zap.Error(err))
		return nil, err
	}
	return &offeringServiceV2.DeletePricingRuleResponse{}, nil
}

func (p PricingRuleController) GetDiscountSetting(ctx context.Context, params *offeringServiceV2.GetDiscountSettingRequest) (*offeringServiceV2.GetDiscountSettingResponse, error) {
	discountSetting, err := p.discountSettingHandler.GetByCompanyId(ctx, params.CompanyId)
	if err != nil {
		zlog.Error(ctx, "failed to get discount setting", zap.Error(err))
		return nil, err
	}

	if discountSetting != nil {
		return &offeringServiceV2.GetDiscountSettingResponse{
			Setting: &offeringModelsV2.DiscountSettingDef{
				ApplyBestOnly: discountSetting.ApplyBestOnly,
				ApplySequence: discountSetting.ApplySequence,
			},
		}, nil
	}

	// upsert
	discountSettingDO := &do.DiscountSettingDO{
		CompanyID:     params.CompanyId,
		ApplyBestOnly: false,
		ApplySequence: []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY},
	}
	err = p.discountSettingHandler.Upsert(ctx, discountSettingDO)
	if err != nil {
		zlog.Error(ctx, "failed to upsert discount setting", zap.Error(err))
		return nil, err
	}

	newDiscountSetting, newErr := p.discountSettingHandler.GetByCompanyId(ctx, params.CompanyId)

	if newDiscountSetting != nil {
		return &offeringServiceV2.GetDiscountSettingResponse{
			Setting: &offeringModelsV2.DiscountSettingDef{
				ApplyBestOnly: newDiscountSetting.ApplyBestOnly,
				ApplySequence: newDiscountSetting.ApplySequence,
			},
		}, nil
	}
	return nil, newErr
}

func (p PricingRuleController) UpdateDiscountSetting(ctx context.Context, params *offeringServiceV2.UpdateDiscountSettingRequest) (*offeringServiceV2.UpdateDiscountSettingResponse, error) {
	discountSetting, err := p.discountSettingHandler.GetByCompanyId(ctx, params.CompanyId)
	if err != nil {
		zlog.Error(ctx, "failed to get discount setting", zap.Error(err))
		return nil, err
	}

	discountSettingDO := &do.DiscountSettingDO{
		ID:            discountSetting.ID,
		CompanyID:     params.CompanyId,
		ApplyBestOnly: params.Setting.ApplyBestOnly,
		ApplySequence: params.Setting.ApplySequence,
		UpdatedBy:     &params.StaffId,
	}

	err = p.discountSettingHandler.Upsert(ctx, discountSettingDO)
	if err != nil {
		zlog.Error(ctx, "failed to update discount setting", zap.Error(err))
		return nil, err
	}

	newDiscountSetting, newErr := p.discountSettingHandler.GetByCompanyId(ctx, params.CompanyId)

	if newDiscountSetting != nil {
		return &offeringServiceV2.UpdateDiscountSettingResponse{
			Setting: &offeringModelsV2.DiscountSettingDef{
				ApplyBestOnly: newDiscountSetting.ApplyBestOnly,
				ApplySequence: newDiscountSetting.ApplySequence,
			},
		}, nil
	}
	return nil, newErr

}

func (p PricingRuleController) getServices(ctx context.Context, petDetailCalculateDOS []*do.CalculateDO) ([]do.ServiceBrief, error) {
	// get hit pricing rules
	serviceIds := lo.Map(petDetailCalculateDOS, func(item *do.CalculateDO, _ int) int64 {
		return item.ServiceId
	})
	uniqServiceIds := lo.Uniq(serviceIds)

	services, err := p.serviceHandler.GetServiceBriefListByIds(ctx, uniqServiceIds)
	if err != nil {
		zlog.Error(ctx, "failed to get service brief list by ids", zap.Error(err))
		return nil, err
	}
	return services, nil
}

func setServiceItemType(petDetailCalculateDOS []*do.CalculateDO, services []do.ServiceBrief) []*do.CalculateDO {
	servicesMap := lo.KeyBy(services, func(item do.ServiceBrief) int64 {
		return item.ServiceId
	})
	for _, item := range petDetailCalculateDOS {
		if _, ok := servicesMap[item.ServiceId]; !ok {
			continue
		}
		item.ServiceItemType = servicesMap[item.ServiceId].ServiceItemType
	}
	return petDetailCalculateDOS
}

func (p PricingRuleController) getHitPricingRules(ctx context.Context, companyId int64, services []do.ServiceBrief) ([]*do.PricingRuleRecordDO, error) {
	// get best pricing rule
	rules, _, _ := p.pricingRuleRecordHandler.ListByPage(ctx, &models.PricingRuleRecordWhereOpt{
		CompanyID: companyId,
		IsActive:  proto.Bool(true),
	}, &utilsV2.PaginationRequest{
		PageSize: proto.Int32(1000),
		PageNum:  proto.Int32(1),
	})

	hitRules := make([]*do.PricingRuleRecordDO, 0)
	for _, rule := range rules {
		for _, serviceBrief := range services {
			if lo.Contains(hitRules, rule) {
				continue
			}
			serviceId := serviceBrief.ServiceId
			careType := serviceBrief.ServiceItemType
			if (rule.AllBoardingApplicable && careType == offeringModelsV1.ServiceItemType_BOARDING) ||
				(rule.AllDaycareApplicable && careType == offeringModelsV1.ServiceItemType_DAYCARE) ||
				lo.Contains(rule.SelectedBoardingServices, serviceId) || lo.Contains(rule.SelectedDaycareServices, serviceId) {
				hitRules = append(hitRules, rule)
			}
		}
	}
	return hitRules, nil
}

func requestToDo(request *offeringServiceV2.UpsertPricingRuleRequest) *do.PricingRuleRecordDO {
	def := request.GetPricingRuleDef()

	return &do.PricingRuleRecordDO{
		ID:                       def.Id,
		CompanyID:                request.CompanyId,
		RuleType:                 def.Type,
		RuleName:                 def.RuleName,
		IsActive:                 def.IsActive,
		AllBoardingApplicable:    def.AllBoardingApplicable,
		SelectedBoardingServices: def.SelectedBoardingServices,
		AllDaycareApplicable:     def.AllDaycareApplicable,
		SelectedDaycareServices:  def.SelectedDaycareServices,
		RuleApplyType:            def.RuleApplyType,
		NeedInSameLodging:        def.NeedInSameLodging,
		RuleConfiguration:        def.RuleConfiguration,
		UpdatedBy:                request.StaffId,
		CreatedAt:                TimestampPtrToTimePtr(def.CreatedAt, lo.ToPtr(time.Now())),
		UpdatedAt:                TimestampPtrToTimePtr(def.UpdatedAt, lo.ToPtr(time.Now())),
		DeletedAt: func() gorm.DeletedAt {
			if def.DeletedAt != nil {
				t := def.DeletedAt.AsTime()
				return gorm.DeletedAt{Time: t, Valid: true}
			}
			return gorm.DeletedAt{Valid: false}
		}(),
	}
}

func doToModel(do *do.PricingRuleRecordDO) *offeringModelsV2.PricingRule {
	return &offeringModelsV2.PricingRule{
		Id:                       *do.ID,
		CompanyId:                do.CompanyID,
		Type:                     do.RuleType,
		RuleName:                 do.RuleName,
		IsActive:                 do.IsActive,
		AllBoardingApplicable:    do.AllBoardingApplicable,
		SelectedBoardingServices: do.SelectedBoardingServices,
		AllDaycareApplicable:     do.AllDaycareApplicable,
		SelectedDaycareServices:  do.SelectedDaycareServices,
		RuleApplyType:            do.RuleApplyType,
		NeedInSameLodging:        do.NeedInSameLodging,
		RuleConfiguration:        do.RuleConfiguration,
		UpdatedBy:                do.UpdatedBy,
		CreatedAt:                timestamppb.New(*do.CreatedAt),
		UpdatedAt:                timestamppb.New(*do.UpdatedAt),
	}
}

func doListToModelList(rules []*do.PricingRuleRecordDO) []*offeringModelsV2.PricingRule {
	ruleModels := make([]*offeringModelsV2.PricingRule, 0)
	if len(rules) == 0 {
		return ruleModels
	}
	for _, rule := range rules {
		ruleModels = append(ruleModels, doToModel(rule))
	}
	return ruleModels
}

func NewPricingRuleController() *PricingRuleController {
	return &PricingRuleController{
		serviceHandler:           service.NewServiceHandler(),
		discountSettingHandler:   service.NewDiscountSettingHandler(),
		pricingRuleRecordHandler: service.NewPricingRuleRecordHandler(),
	}
}

func TimestampPtrToTimePtr(ts *timestamppb.Timestamp, defaultTime *time.Time) *time.Time {
	if ts == nil {
		return defaultTime
	}
	t := ts.AsTime()
	return &t
}
