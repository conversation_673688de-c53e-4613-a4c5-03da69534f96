package do

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
)

type PricingRuleRecordDO struct {
	ID                       *int64
	CompanyID                int64
	RuleType                 offeringModelsV2.RuleType
	RuleName                 string
	IsActive                 bool
	AllBoardingApplicable    bool
	SelectedBoardingServices []int64
	AllDaycareApplicable     bool
	SelectedDaycareServices  []int64
	RuleApplyType            offeringModelsV2.RuleApplyType
	NeedInSameLodging        bool
	RuleConfiguration        *offeringModelsV2.PricingRuleConfiguration
	UpdatedBy                int64
	CreatedAt                *time.Time
	UpdatedAt                *time.Time
	DeletedAt                gorm.DeletedAt
}

type CalculateCompositeDO struct {
	// pet detail calculate do list
	CalculateDOS []*CalculateDO
	// pricing rule model
	PricingRuleRecordDOS []*PricingRuleRecordDO
	// for preview
	IsPreview bool
	// company id
	CompanyID int64
	// apply best only
	ApplyBestOnly bool
	// apply sequence
	ApplySequence []offeringModelsV2.RuleType
}

type CalculateDO struct {
	// pet id
	PetId int64
	// service id
	ServiceId int64
	// service price
	ServicePrice decimal.Decimal
	// lodging unit id
	LodgingUnitId *int64
	// scope type price
	ScopeTypePrice *offeringModelsV1.ServiceScopeType
	// service date
	ServiceDate *string
	// hit pricing rule
	IsHitPricingRule bool
	// hit pricing rule ids
	UsedPricingRuleIds []int64
	// service item type
	ServiceItemType offeringModelsV1.ServiceItemType
	// service price
	AdjustedPrice decimal.Decimal
	// pricing rule to price difference
	PricingRuleIdPriceMap map[int64]decimal.Decimal
}

type CalculateResultDO struct {
	// pet id
	PetId int64
	// service id
	ServiceId int64
	// service price
	AdjustedPrice decimal.Decimal
	// used pricing rule ids
	UsedPricingRuleIds []int64
	// service date
	ServiceDate *string
}

type CalculateCompositeResultDO struct {
	// pet detail calculate do list
	CalculateResultDOS []*CalculateResultDO
	// formula string
	Formula *string
}
