// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: EvaluationRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_evaluation_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository EvaluationRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockEvaluationRepository is a mock of EvaluationRepository interface.
type MockEvaluationRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEvaluationRepositoryMockRecorder
	isgomock struct{}
}

// MockEvaluationRepositoryMockRecorder is the mock recorder for MockEvaluationRepository.
type MockEvaluationRepositoryMockRecorder struct {
	mock *MockEvaluationRepository
}

// NewMockEvaluationRepository creates a new mock instance.
func NewMockEvaluationRepository(ctrl *gomock.Controller) *MockEvaluationRepository {
	mock := &MockEvaluationRepository{ctrl: ctrl}
	mock.recorder = &MockEvaluationRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEvaluationRepository) EXPECT() *MockEvaluationRepositoryMockRecorder {
	return m.recorder
}

// AddNewEvaluation mocks base method.
func (m *MockEvaluationRepository) AddNewEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, do *do.EvaluationDO) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNewEvaluation", ctx, tx, companyId, do)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNewEvaluation indicates an expected call of AddNewEvaluation.
func (mr *MockEvaluationRepositoryMockRecorder) AddNewEvaluation(ctx, tx, companyId, do any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNewEvaluation", reflect.TypeOf((*MockEvaluationRepository)(nil).AddNewEvaluation), ctx, tx, companyId, do)
}

// GetEvaluationDetail mocks base method.
func (m *MockEvaluationRepository) GetEvaluationDetail(ctx context.Context, evaluationId int64) (*do.EvaluationDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEvaluationDetail", ctx, evaluationId)
	ret0, _ := ret[0].(*do.EvaluationDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEvaluationDetail indicates an expected call of GetEvaluationDetail.
func (mr *MockEvaluationRepositoryMockRecorder) GetEvaluationDetail(ctx, evaluationId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEvaluationDetail", reflect.TypeOf((*MockEvaluationRepository)(nil).GetEvaluationDetail), ctx, evaluationId)
}

// GetEvaluationList mocks base method.
func (m *MockEvaluationRepository) GetEvaluationList(ctx context.Context, companyId int64) ([]*do.EvaluationDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEvaluationList", ctx, companyId)
	ret0, _ := ret[0].([]*do.EvaluationDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEvaluationList indicates an expected call of GetEvaluationList.
func (mr *MockEvaluationRepositoryMockRecorder) GetEvaluationList(ctx, companyId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEvaluationList", reflect.TypeOf((*MockEvaluationRepository)(nil).GetEvaluationList), ctx, companyId)
}

// GetEvaluationListByFilter mocks base method.
func (m *MockEvaluationRepository) GetEvaluationListByFilter(ctx context.Context, filter *do.EvaluationModelFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.EvaluationDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEvaluationListByFilter", ctx, filter, paginationRequest)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*do.EvaluationDO)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetEvaluationListByFilter indicates an expected call of GetEvaluationListByFilter.
func (mr *MockEvaluationRepositoryMockRecorder) GetEvaluationListByFilter(ctx, filter, paginationRequest any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEvaluationListByFilter", reflect.TypeOf((*MockEvaluationRepository)(nil).GetEvaluationListByFilter), ctx, filter, paginationRequest)
}

// GetEvaluationListByIds mocks base method.
func (m *MockEvaluationRepository) GetEvaluationListByIds(ctx context.Context, evaluationIds []int64) ([]*do.EvaluationDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEvaluationListByIds", ctx, evaluationIds)
	ret0, _ := ret[0].([]*do.EvaluationDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEvaluationListByIds indicates an expected call of GetEvaluationListByIds.
func (mr *MockEvaluationRepositoryMockRecorder) GetEvaluationListByIds(ctx, evaluationIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEvaluationListByIds", reflect.TypeOf((*MockEvaluationRepository)(nil).GetEvaluationListByIds), ctx, evaluationIds)
}

// UpdateEvaluation mocks base method.
func (m *MockEvaluationRepository) UpdateEvaluation(ctx context.Context, tx *gorm.DB, companyId, evaluationId int64, updateOpt *do.EvaluationDO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEvaluation", ctx, tx, companyId, evaluationId, updateOpt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEvaluation indicates an expected call of UpdateEvaluation.
func (mr *MockEvaluationRepositoryMockRecorder) UpdateEvaluation(ctx, tx, companyId, evaluationId, updateOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEvaluation", reflect.TypeOf((*MockEvaluationRepository)(nil).UpdateEvaluation), ctx, tx, companyId, evaluationId, updateOpt)
}
