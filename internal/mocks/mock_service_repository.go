// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServiceRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceRepository is a mock of ServiceRepository interface.
type MockServiceRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceRepositoryMockRecorder is the mock recorder for MockServiceRepository.
type MockServiceRepositoryMockRecorder struct {
	mock *MockServiceRepository
}

// NewMockServiceRepository creates a new mock instance.
func NewMockServiceRepository(ctrl *gomock.Controller) *MockServiceRepository {
	mock := &MockServiceRepository{ctrl: ctrl}
	mock.recorder = &MockServiceRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceRepository) EXPECT() *MockServiceRepositoryMockRecorder {
	return m.recorder
}

// AddNewService mocks base method.
func (m *MockServiceRepository) AddNewService(ctx context.Context, companyId int64, service *do.ServiceDO) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNewService", ctx, companyId, service)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNewService indicates an expected call of AddNewService.
func (mr *MockServiceRepositoryMockRecorder) AddNewService(ctx, companyId, service any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNewService", reflect.TypeOf((*MockServiceRepository)(nil).AddNewService), ctx, companyId, service)
}

// GetAllBriefServices mocks base method.
func (m *MockServiceRepository) GetAllBriefServices(ctx context.Context, companyId int64) ([]do.ServiceBrief, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllBriefServices", ctx, companyId)
	ret0, _ := ret[0].([]do.ServiceBrief)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllBriefServices indicates an expected call of GetAllBriefServices.
func (mr *MockServiceRepositoryMockRecorder) GetAllBriefServices(ctx, companyId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBriefServices", reflect.TypeOf((*MockServiceRepository)(nil).GetAllBriefServices), ctx, companyId)
}

// GetApplicableAddon mocks base method.
func (m *MockServiceRepository) GetApplicableAddon(ctx context.Context, companyId, petId int64, applicableAddonFilter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicableAddon", ctx, companyId, petId, applicableAddonFilter, paginationRequest)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]do.ServiceWithOverrideRules)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetApplicableAddon indicates an expected call of GetApplicableAddon.
func (mr *MockServiceRepositoryMockRecorder) GetApplicableAddon(ctx, companyId, petId, applicableAddonFilter, paginationRequest any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicableAddon", reflect.TypeOf((*MockServiceRepository)(nil).GetApplicableAddon), ctx, companyId, petId, applicableAddonFilter, paginationRequest)
}

// GetApplicableService mocks base method.
func (m *MockServiceRepository) GetApplicableService(ctx context.Context, companyId, petId int64, applicableServiceFilter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicableService", ctx, companyId, petId, applicableServiceFilter, paginationRequest)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]do.ServiceWithOverrideRules)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetApplicableService indicates an expected call of GetApplicableService.
func (mr *MockServiceRepositoryMockRecorder) GetApplicableService(ctx, companyId, petId, applicableServiceFilter, paginationRequest any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicableService", reflect.TypeOf((*MockServiceRepository)(nil).GetApplicableService), ctx, companyId, petId, applicableServiceFilter, paginationRequest)
}

// GetBriefServiceListWithServiceIds mocks base method.
func (m *MockServiceRepository) GetBriefServiceListWithServiceIds(ctx context.Context, serviceIdList []int64) ([]do.ServiceBrief, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBriefServiceListWithServiceIds", ctx, serviceIdList)
	ret0, _ := ret[0].([]do.ServiceBrief)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBriefServiceListWithServiceIds indicates an expected call of GetBriefServiceListWithServiceIds.
func (mr *MockServiceRepositoryMockRecorder) GetBriefServiceListWithServiceIds(ctx, serviceIdList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBriefServiceListWithServiceIds", reflect.TypeOf((*MockServiceRepository)(nil).GetBriefServiceListWithServiceIds), ctx, serviceIdList)
}

// GetCustomizedServiceList mocks base method.
func (m *MockServiceRepository) GetCustomizedServiceList(ctx context.Context, companyId, petId int64) ([]do.PetCustomizedRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomizedServiceList", ctx, companyId, petId)
	ret0, _ := ret[0].([]do.PetCustomizedRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomizedServiceList indicates an expected call of GetCustomizedServiceList.
func (mr *MockServiceRepositoryMockRecorder) GetCustomizedServiceList(ctx, companyId, petId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomizedServiceList", reflect.TypeOf((*MockServiceRepository)(nil).GetCustomizedServiceList), ctx, companyId, petId)
}

// GetService mocks base method.
func (m *MockServiceRepository) GetService(ctx context.Context, serviceId int64) (*models.MoeGroomingService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetService", ctx, serviceId)
	ret0, _ := ret[0].(*models.MoeGroomingService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetService indicates an expected call of GetService.
func (mr *MockServiceRepositoryMockRecorder) GetService(ctx, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetService", reflect.TypeOf((*MockServiceRepository)(nil).GetService), ctx, serviceId)
}

// GetServiceDetail mocks base method.
func (m *MockServiceRepository) GetServiceDetail(ctx context.Context, serviceId int64) (*do.ServiceDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceDetail", ctx, serviceId)
	ret0, _ := ret[0].(*do.ServiceDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceDetail indicates an expected call of GetServiceDetail.
func (mr *MockServiceRepositoryMockRecorder) GetServiceDetail(ctx, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceDetail", reflect.TypeOf((*MockServiceRepository)(nil).GetServiceDetail), ctx, serviceId)
}

// GetServiceWithOverrideRule mocks base method.
func (m *MockServiceRepository) GetServiceWithOverrideRule(ctx context.Context, companyId int64, businessId *int64, serviceIdWithPetIdList map[int64][]int64) (int64, []do.ServiceWithOverrideRules, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceWithOverrideRule", ctx, companyId, businessId, serviceIdWithPetIdList)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]do.ServiceWithOverrideRules)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetServiceWithOverrideRule indicates an expected call of GetServiceWithOverrideRule.
func (mr *MockServiceRepositoryMockRecorder) GetServiceWithOverrideRule(ctx, companyId, businessId, serviceIdWithPetIdList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceWithOverrideRule", reflect.TypeOf((*MockServiceRepository)(nil).GetServiceWithOverrideRule), ctx, companyId, businessId, serviceIdWithPetIdList)
}

// GetServicesWithFilterPaginated mocks base method.
func (m *MockServiceRepository) GetServicesWithFilterPaginated(ctx context.Context, companyId int64, filter do.ServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest, orderBy offeringpb.ServiceOrderByType) (int64, []*do.ServiceDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServicesWithFilterPaginated", ctx, companyId, filter, paginationRequest, orderBy)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*do.ServiceDO)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetServicesWithFilterPaginated indicates an expected call of GetServicesWithFilterPaginated.
func (mr *MockServiceRepositoryMockRecorder) GetServicesWithFilterPaginated(ctx, companyId, filter, paginationRequest, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServicesWithFilterPaginated", reflect.TypeOf((*MockServiceRepository)(nil).GetServicesWithFilterPaginated), ctx, companyId, filter, paginationRequest, orderBy)
}

// IsServiceIdInCompany mocks base method.
func (m *MockServiceRepository) IsServiceIdInCompany(ctx context.Context, serviceId, companyId int64, withDeleted bool) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsServiceIdInCompany", ctx, serviceId, companyId, withDeleted)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsServiceIdInCompany indicates an expected call of IsServiceIdInCompany.
func (mr *MockServiceRepositoryMockRecorder) IsServiceIdInCompany(ctx, serviceId, companyId, withDeleted any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsServiceIdInCompany", reflect.TypeOf((*MockServiceRepository)(nil).IsServiceIdInCompany), ctx, serviceId, companyId, withDeleted)
}

// IsServiceNameExist mocks base method.
func (m *MockServiceRepository) IsServiceNameExist(ctx context.Context, companyId int64, service do.UniqueService) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsServiceNameExist", ctx, companyId, service)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsServiceNameExist indicates an expected call of IsServiceNameExist.
func (mr *MockServiceRepositoryMockRecorder) IsServiceNameExist(ctx, companyId, service any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsServiceNameExist", reflect.TypeOf((*MockServiceRepository)(nil).IsServiceNameExist), ctx, companyId, service)
}

// OverrideServiceByPet mocks base method.
func (m *MockServiceRepository) OverrideServiceByPet(ctx context.Context, companyId int64, serviceWithPetOverrideRules map[int64][]do.PetOverrideRule) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OverrideServiceByPet", ctx, companyId, serviceWithPetOverrideRules)
	ret0, _ := ret[0].(error)
	return ret0
}

// OverrideServiceByPet indicates an expected call of OverrideServiceByPet.
func (mr *MockServiceRepositoryMockRecorder) OverrideServiceByPet(ctx, companyId, serviceWithPetOverrideRules any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OverrideServiceByPet", reflect.TypeOf((*MockServiceRepository)(nil).OverrideServiceByPet), ctx, companyId, serviceWithPetOverrideRules)
}

// UpdateService mocks base method.
func (m *MockServiceRepository) UpdateService(ctx context.Context, companyId int64, updateOpt *do.ServiceUpdateOpt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateService", ctx, companyId, updateOpt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateService indicates an expected call of UpdateService.
func (mr *MockServiceRepositoryMockRecorder) UpdateService(ctx, companyId, updateOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateService", reflect.TypeOf((*MockServiceRepository)(nil).UpdateService), ctx, companyId, updateOpt)
}
