// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1 (interfaces: StaffServiceClient)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_staff_service_client.go github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1 StaffServiceClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockStaffServiceClient is a mock of StaffServiceClient interface.
type MockStaffServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockStaffServiceClientMockRecorder
	isgomock struct{}
}

// MockStaffServiceClientMockRecorder is the mock recorder for MockStaffServiceClient.
type MockStaffServiceClientMockRecorder struct {
	mock *MockStaffServiceClient
}

// NewMockStaffServiceClient creates a new mock instance.
func NewMockStaffServiceClient(ctrl *gomock.Controller) *MockStaffServiceClient {
	mock := &MockStaffServiceClient{ctrl: ctrl}
	mock.recorder = &MockStaffServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStaffServiceClient) EXPECT() *MockStaffServiceClientMockRecorder {
	return m.recorder
}

// CheckStaffLoginTime mocks base method.
func (m *MockStaffServiceClient) CheckStaffLoginTime(ctx context.Context, in *organizationsvcpb.CheckStaffLoginTimeRequest, opts ...grpc.CallOption) (*organizationsvcpb.CheckStaffLoginTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStaffLoginTime", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CheckStaffLoginTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStaffLoginTime indicates an expected call of CheckStaffLoginTime.
func (mr *MockStaffServiceClientMockRecorder) CheckStaffLoginTime(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStaffLoginTime", reflect.TypeOf((*MockStaffServiceClient)(nil).CheckStaffLoginTime), varargs...)
}

// CountStaffWithRole mocks base method.
func (m *MockStaffServiceClient) CountStaffWithRole(ctx context.Context, in *organizationsvcpb.CountStaffWithRoleRequest, opts ...grpc.CallOption) (*organizationsvcpb.CountStaffWithRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CountStaffWithRole", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CountStaffWithRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountStaffWithRole indicates an expected call of CountStaffWithRole.
func (mr *MockStaffServiceClientMockRecorder) CountStaffWithRole(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountStaffWithRole", reflect.TypeOf((*MockStaffServiceClient)(nil).CountStaffWithRole), varargs...)
}

// CreateEnterpriseOwner mocks base method.
func (m *MockStaffServiceClient) CreateEnterpriseOwner(ctx context.Context, in *organizationsvcpb.CreateEnterpriseOwnerRequest, opts ...grpc.CallOption) (*organizationsvcpb.CreateEnterpriseOwnerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateEnterpriseOwner", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CreateEnterpriseOwnerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEnterpriseOwner indicates an expected call of CreateEnterpriseOwner.
func (mr *MockStaffServiceClientMockRecorder) CreateEnterpriseOwner(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEnterpriseOwner", reflect.TypeOf((*MockStaffServiceClient)(nil).CreateEnterpriseOwner), varargs...)
}

// CreateEnterpriseStaff mocks base method.
func (m *MockStaffServiceClient) CreateEnterpriseStaff(ctx context.Context, in *organizationsvcpb.CreateEnterpriseStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.CreateEnterpriseStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateEnterpriseStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CreateEnterpriseStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEnterpriseStaff indicates an expected call of CreateEnterpriseStaff.
func (mr *MockStaffServiceClientMockRecorder) CreateEnterpriseStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEnterpriseStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).CreateEnterpriseStaff), varargs...)
}

// CreateStaff mocks base method.
func (m *MockStaffServiceClient) CreateStaff(ctx context.Context, in *organizationsvcpb.CreateStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.CreateStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CreateStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStaff indicates an expected call of CreateStaff.
func (mr *MockStaffServiceClientMockRecorder) CreateStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).CreateStaff), varargs...)
}

// CreateStaffRecord mocks base method.
func (m *MockStaffServiceClient) CreateStaffRecord(ctx context.Context, in *organizationsvcpb.CreateStaffRecordRequest, opts ...grpc.CallOption) (*organizationsvcpb.CreateStaffRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateStaffRecord", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CreateStaffRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStaffRecord indicates an expected call of CreateStaffRecord.
func (mr *MockStaffServiceClientMockRecorder) CreateStaffRecord(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStaffRecord", reflect.TypeOf((*MockStaffServiceClient)(nil).CreateStaffRecord), varargs...)
}

// DeleteEnterpriseStaff mocks base method.
func (m *MockStaffServiceClient) DeleteEnterpriseStaff(ctx context.Context, in *organizationsvcpb.DeleteEnterpriseStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.DeleteEnterpriseStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEnterpriseStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.DeleteEnterpriseStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEnterpriseStaff indicates an expected call of DeleteEnterpriseStaff.
func (mr *MockStaffServiceClientMockRecorder) DeleteEnterpriseStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEnterpriseStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).DeleteEnterpriseStaff), varargs...)
}

// DeleteStaff mocks base method.
func (m *MockStaffServiceClient) DeleteStaff(ctx context.Context, in *organizationsvcpb.DeleteStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.DeleteStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.DeleteStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteStaff indicates an expected call of DeleteStaff.
func (mr *MockStaffServiceClientMockRecorder) DeleteStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).DeleteStaff), varargs...)
}

// GetClockInOutStaffs mocks base method.
func (m *MockStaffServiceClient) GetClockInOutStaffs(ctx context.Context, in *organizationsvcpb.GetClockInOutStaffsRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetClockInOutStaffsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClockInOutStaffs", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetClockInOutStaffsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClockInOutStaffs indicates an expected call of GetClockInOutStaffs.
func (mr *MockStaffServiceClientMockRecorder) GetClockInOutStaffs(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClockInOutStaffs", reflect.TypeOf((*MockStaffServiceClient)(nil).GetClockInOutStaffs), varargs...)
}

// GetEnterpriseStaff mocks base method.
func (m *MockStaffServiceClient) GetEnterpriseStaff(ctx context.Context, in *organizationsvcpb.GetEnterpriseStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetEnterpriseStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEnterpriseStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetEnterpriseStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnterpriseStaff indicates an expected call of GetEnterpriseStaff.
func (mr *MockStaffServiceClientMockRecorder) GetEnterpriseStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnterpriseStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).GetEnterpriseStaff), varargs...)
}

// GetEnterpriseStaffsByAccountId mocks base method.
func (m *MockStaffServiceClient) GetEnterpriseStaffsByAccountId(ctx context.Context, in *organizationsvcpb.GetEnterpriseStaffsByAccountIdRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetEnterpriseStaffsByAccountIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEnterpriseStaffsByAccountId", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetEnterpriseStaffsByAccountIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnterpriseStaffsByAccountId indicates an expected call of GetEnterpriseStaffsByAccountId.
func (mr *MockStaffServiceClientMockRecorder) GetEnterpriseStaffsByAccountId(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnterpriseStaffsByAccountId", reflect.TypeOf((*MockStaffServiceClient)(nil).GetEnterpriseStaffsByAccountId), varargs...)
}

// GetEnterpriseStaffsByWorkingLocationIds mocks base method.
func (m *MockStaffServiceClient) GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *organizationsvcpb.GetEnterpriseStaffsByWorkingLocationIdsRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetEnterpriseStaffsByWorkingLocationIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEnterpriseStaffsByWorkingLocationIds", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetEnterpriseStaffsByWorkingLocationIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnterpriseStaffsByWorkingLocationIds indicates an expected call of GetEnterpriseStaffsByWorkingLocationIds.
func (mr *MockStaffServiceClientMockRecorder) GetEnterpriseStaffsByWorkingLocationIds(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnterpriseStaffsByWorkingLocationIds", reflect.TypeOf((*MockStaffServiceClient)(nil).GetEnterpriseStaffsByWorkingLocationIds), varargs...)
}

// GetShowOnCalendarStaffIds mocks base method.
func (m *MockStaffServiceClient) GetShowOnCalendarStaffIds(ctx context.Context, in *organizationsvcpb.GetShowOnCalendarStaffsRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetShowOnCalendarStaffIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowOnCalendarStaffIds", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetShowOnCalendarStaffIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowOnCalendarStaffIds indicates an expected call of GetShowOnCalendarStaffIds.
func (mr *MockStaffServiceClientMockRecorder) GetShowOnCalendarStaffIds(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowOnCalendarStaffIds", reflect.TypeOf((*MockStaffServiceClient)(nil).GetShowOnCalendarStaffIds), varargs...)
}

// GetShowOnCalendarStaffs mocks base method.
func (m *MockStaffServiceClient) GetShowOnCalendarStaffs(ctx context.Context, in *organizationsvcpb.GetShowOnCalendarStaffsRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetShowOnCalendarStaffsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowOnCalendarStaffs", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetShowOnCalendarStaffsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowOnCalendarStaffs indicates an expected call of GetShowOnCalendarStaffs.
func (mr *MockStaffServiceClientMockRecorder) GetShowOnCalendarStaffs(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowOnCalendarStaffs", reflect.TypeOf((*MockStaffServiceClient)(nil).GetShowOnCalendarStaffs), varargs...)
}

// GetStaffByCompany mocks base method.
func (m *MockStaffServiceClient) GetStaffByCompany(ctx context.Context, in *organizationsvcpb.GetStaffByCompanyRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffByCompanyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffByCompany", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffByCompanyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffByCompany indicates an expected call of GetStaffByCompany.
func (mr *MockStaffServiceClientMockRecorder) GetStaffByCompany(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffByCompany", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffByCompany), varargs...)
}

// GetStaffByPhoneNumber mocks base method.
func (m *MockStaffServiceClient) GetStaffByPhoneNumber(ctx context.Context, in *organizationsvcpb.GetStaffByPhoneNumberRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffByPhoneNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffByPhoneNumber", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffByPhoneNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffByPhoneNumber indicates an expected call of GetStaffByPhoneNumber.
func (mr *MockStaffServiceClientMockRecorder) GetStaffByPhoneNumber(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffByPhoneNumber", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffByPhoneNumber), varargs...)
}

// GetStaffDetail mocks base method.
func (m *MockStaffServiceClient) GetStaffDetail(ctx context.Context, in *organizationsvcpb.GetStaffDetailRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffDetail", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffDetail indicates an expected call of GetStaffDetail.
func (mr *MockStaffServiceClientMockRecorder) GetStaffDetail(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffDetail", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffDetail), varargs...)
}

// GetStaffFullDetail mocks base method.
func (m *MockStaffServiceClient) GetStaffFullDetail(ctx context.Context, in *organizationsvcpb.GetStaffFullDetailRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffFullDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffFullDetail", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffFullDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffFullDetail indicates an expected call of GetStaffFullDetail.
func (mr *MockStaffServiceClientMockRecorder) GetStaffFullDetail(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffFullDetail", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffFullDetail), varargs...)
}

// GetStaffLoginTime mocks base method.
func (m *MockStaffServiceClient) GetStaffLoginTime(ctx context.Context, in *organizationsvcpb.GetStaffLoginTimeRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffLoginTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffLoginTime", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffLoginTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffLoginTime indicates an expected call of GetStaffLoginTime.
func (mr *MockStaffServiceClientMockRecorder) GetStaffLoginTime(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffLoginTime", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffLoginTime), varargs...)
}

// GetStaffsByAccountId mocks base method.
func (m *MockStaffServiceClient) GetStaffsByAccountId(ctx context.Context, in *organizationsvcpb.GetStaffsByAccountIdRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffsByAccountIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffsByAccountId", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffsByAccountIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffsByAccountId indicates an expected call of GetStaffsByAccountId.
func (mr *MockStaffServiceClientMockRecorder) GetStaffsByAccountId(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffsByAccountId", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffsByAccountId), varargs...)
}

// GetStaffsByRole mocks base method.
func (m *MockStaffServiceClient) GetStaffsByRole(ctx context.Context, in *organizationsvcpb.GetStaffsByRoleRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffsByRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffsByRole", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffsByRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffsByRole indicates an expected call of GetStaffsByRole.
func (mr *MockStaffServiceClientMockRecorder) GetStaffsByRole(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffsByRole", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffsByRole), varargs...)
}

// GetStaffsByWorkingLocation mocks base method.
func (m *MockStaffServiceClient) GetStaffsByWorkingLocation(ctx context.Context, in *organizationsvcpb.GetStaffsByWorkingLocationRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffsByWorkingLocationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffsByWorkingLocation", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffsByWorkingLocationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffsByWorkingLocation indicates an expected call of GetStaffsByWorkingLocation.
func (mr *MockStaffServiceClientMockRecorder) GetStaffsByWorkingLocation(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffsByWorkingLocation", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffsByWorkingLocation), varargs...)
}

// GetStaffsByWorkingLocationIds mocks base method.
func (m *MockStaffServiceClient) GetStaffsByWorkingLocationIds(ctx context.Context, in *organizationsvcpb.GetStaffsByWorkingLocationIdsRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetStaffsByWorkingLocationIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStaffsByWorkingLocationIds", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetStaffsByWorkingLocationIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffsByWorkingLocationIds indicates an expected call of GetStaffsByWorkingLocationIds.
func (mr *MockStaffServiceClientMockRecorder) GetStaffsByWorkingLocationIds(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffsByWorkingLocationIds", reflect.TypeOf((*MockStaffServiceClient)(nil).GetStaffsByWorkingLocationIds), varargs...)
}

// ListOwnerStaffInfo mocks base method.
func (m *MockStaffServiceClient) ListOwnerStaffInfo(ctx context.Context, in *organizationsvcpb.ListOwnerStaffInfoRequest, opts ...grpc.CallOption) (*organizationsvcpb.ListOwnerStaffInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListOwnerStaffInfo", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.ListOwnerStaffInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOwnerStaffInfo indicates an expected call of ListOwnerStaffInfo.
func (mr *MockStaffServiceClientMockRecorder) ListOwnerStaffInfo(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOwnerStaffInfo", reflect.TypeOf((*MockStaffServiceClient)(nil).ListOwnerStaffInfo), varargs...)
}

// ListStaffEmailDefs mocks base method.
func (m *MockStaffServiceClient) ListStaffEmailDefs(ctx context.Context, in *organizationsvcpb.ListStaffEmailDefsRequest, opts ...grpc.CallOption) (*organizationsvcpb.ListStaffEmailDefsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListStaffEmailDefs", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.ListStaffEmailDefsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStaffEmailDefs indicates an expected call of ListStaffEmailDefs.
func (mr *MockStaffServiceClientMockRecorder) ListStaffEmailDefs(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStaffEmailDefs", reflect.TypeOf((*MockStaffServiceClient)(nil).ListStaffEmailDefs), varargs...)
}

// MigrateStaffData mocks base method.
func (m *MockStaffServiceClient) MigrateStaffData(ctx context.Context, in *organizationsvcpb.MigrateStaffDataRequest, opts ...grpc.CallOption) (*organizationsvcpb.MigrateStaffDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MigrateStaffData", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.MigrateStaffDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MigrateStaffData indicates an expected call of MigrateStaffData.
func (mr *MockStaffServiceClientMockRecorder) MigrateStaffData(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateStaffData", reflect.TypeOf((*MockStaffServiceClient)(nil).MigrateStaffData), varargs...)
}

// QueryStaffByCompanyId mocks base method.
func (m *MockStaffServiceClient) QueryStaffByCompanyId(ctx context.Context, in *organizationsvcpb.QueryStaffByCompanyIdRequest, opts ...grpc.CallOption) (*organizationsvcpb.QueryStaffByCompanyIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryStaffByCompanyId", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.QueryStaffByCompanyIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStaffByCompanyId indicates an expected call of QueryStaffByCompanyId.
func (mr *MockStaffServiceClientMockRecorder) QueryStaffByCompanyId(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStaffByCompanyId", reflect.TypeOf((*MockStaffServiceClient)(nil).QueryStaffByCompanyId), varargs...)
}

// QueryStaffByIds mocks base method.
func (m *MockStaffServiceClient) QueryStaffByIds(ctx context.Context, in *organizationsvcpb.QueryStaffByIdsRequest, opts ...grpc.CallOption) (*organizationsvcpb.QueryStaffByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryStaffByIds", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.QueryStaffByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStaffByIds indicates an expected call of QueryStaffByIds.
func (mr *MockStaffServiceClientMockRecorder) QueryStaffByIds(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStaffByIds", reflect.TypeOf((*MockStaffServiceClient)(nil).QueryStaffByIds), varargs...)
}

// QueryStaffListByPagination mocks base method.
func (m *MockStaffServiceClient) QueryStaffListByPagination(ctx context.Context, in *organizationsvcpb.QueryStaffListByPaginationRequest, opts ...grpc.CallOption) (*organizationsvcpb.QueryStaffListByPaginationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryStaffListByPagination", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.QueryStaffListByPaginationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStaffListByPagination indicates an expected call of QueryStaffListByPagination.
func (mr *MockStaffServiceClientMockRecorder) QueryStaffListByPagination(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStaffListByPagination", reflect.TypeOf((*MockStaffServiceClient)(nil).QueryStaffListByPagination), varargs...)
}

// SendInviteLink mocks base method.
func (m *MockStaffServiceClient) SendInviteLink(ctx context.Context, in *organizationsvcpb.SendInviteLinkRequest, opts ...grpc.CallOption) (*organizationsvcpb.SendInviteLinkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendInviteLink", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.SendInviteLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendInviteLink indicates an expected call of SendInviteLink.
func (mr *MockStaffServiceClientMockRecorder) SendInviteLink(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendInviteLink", reflect.TypeOf((*MockStaffServiceClient)(nil).SendInviteLink), varargs...)
}

// SendInviteStaffLink mocks base method.
func (m *MockStaffServiceClient) SendInviteStaffLink(ctx context.Context, in *organizationsvcpb.SendInviteStaffLinkRequest, opts ...grpc.CallOption) (*organizationsvcpb.SendInviteStaffLinkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendInviteStaffLink", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.SendInviteStaffLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendInviteStaffLink indicates an expected call of SendInviteStaffLink.
func (mr *MockStaffServiceClientMockRecorder) SendInviteStaffLink(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendInviteStaffLink", reflect.TypeOf((*MockStaffServiceClient)(nil).SendInviteStaffLink), varargs...)
}

// UnlinkStaff mocks base method.
func (m *MockStaffServiceClient) UnlinkStaff(ctx context.Context, in *organizationsvcpb.UnlinkStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.UnlinkStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnlinkStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UnlinkStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnlinkStaff indicates an expected call of UnlinkStaff.
func (mr *MockStaffServiceClientMockRecorder) UnlinkStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlinkStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).UnlinkStaff), varargs...)
}

// UpdateAccountLastVisitBusiness mocks base method.
func (m *MockStaffServiceClient) UpdateAccountLastVisitBusiness(ctx context.Context, in *organizationsvcpb.UpdateAccountLastVisitBusinessRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateAccountLastVisitBusinessResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAccountLastVisitBusiness", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateAccountLastVisitBusinessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAccountLastVisitBusiness indicates an expected call of UpdateAccountLastVisitBusiness.
func (mr *MockStaffServiceClientMockRecorder) UpdateAccountLastVisitBusiness(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountLastVisitBusiness", reflect.TypeOf((*MockStaffServiceClient)(nil).UpdateAccountLastVisitBusiness), varargs...)
}

// UpdateEnterpriseStaff mocks base method.
func (m *MockStaffServiceClient) UpdateEnterpriseStaff(ctx context.Context, in *organizationsvcpb.UpdateEnterpriseStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateEnterpriseStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEnterpriseStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateEnterpriseStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEnterpriseStaff indicates an expected call of UpdateEnterpriseStaff.
func (mr *MockStaffServiceClientMockRecorder) UpdateEnterpriseStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEnterpriseStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).UpdateEnterpriseStaff), varargs...)
}

// UpdateStaff mocks base method.
func (m *MockStaffServiceClient) UpdateStaff(ctx context.Context, in *organizationsvcpb.UpdateStaffRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateStaffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStaff", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateStaffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStaff indicates an expected call of UpdateStaff.
func (mr *MockStaffServiceClientMockRecorder) UpdateStaff(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStaff", reflect.TypeOf((*MockStaffServiceClient)(nil).UpdateStaff), varargs...)
}

// UpdateStaffRecord mocks base method.
func (m *MockStaffServiceClient) UpdateStaffRecord(ctx context.Context, in *organizationsvcpb.UpdateStaffRecordRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateStaffRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStaffRecord", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateStaffRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStaffRecord indicates an expected call of UpdateStaffRecord.
func (mr *MockStaffServiceClientMockRecorder) UpdateStaffRecord(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStaffRecord", reflect.TypeOf((*MockStaffServiceClient)(nil).UpdateStaffRecord), varargs...)
}
