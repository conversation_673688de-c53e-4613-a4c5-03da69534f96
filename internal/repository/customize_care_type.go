package repository

import (
	"context"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gormx"
	"strings"
	"time"
	"unicode"
)

type CustomizeCareTypeRepository interface {
	List(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt) ([]*do.CustomizeCareTypeDO, error)
	Init(ctx context.Context, companyId int64, staffId int64,
		isAllowBoardingAndDaycare bool, isAllowDogWalking bool, isAllowGroupClass bool) ([]*do.CustomizeCareTypeDO, error)
	Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error
	IsNameDuplicate(ctx context.Context, companyId int64, name string, excludeId *int64) (bool, error)
}

type customizeCareTypeRepository struct {
	db *gorm.DB
}

func NewCustomizeCareTypeRepository(db *gorm.DB) CustomizeCareTypeRepository {
	return &customizeCareTypeRepository{
		db: db,
	}
}

func (c customizeCareTypeRepository) Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error {
	if whereOpt == nil || updateOpt == nil {
		return errors.New("whereOpt and updateOpt cannot be nil")
	}
	whereQuery := gormx.Query(whereOpt)
	if whereQuery == nil {
		return errors.New("empty where condition is not allowed")
	}
	if err := c.db.WithContext(ctx).Model(&models.CustomizeCareType{}).Where(gormx.Query(whereOpt)).Updates(gormx.Update(updateOpt)).Error; err != nil {
		return errors.Wrap(err, "failed to update customize care types")
	}
	return nil
}

func (c customizeCareTypeRepository) List(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt) ([]*do.CustomizeCareTypeDO, error) {
	records := make([]*models.CustomizeCareType, 0)

	query := c.db.WithContext(ctx)
	if whereOpt.CompanyID != nil {
		query = query.Where("company_id = ?", *whereOpt.CompanyID)
	}
	if len(whereOpt.ServiceItemTypes) > 0 {
		query = query.Where("service_item_type IN ?", whereOpt.ServiceItemTypes)
	}
	query = query.Order("sort ASC")

	if err := query.Find(&records).Error; err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertCustomizeCareTypesToDOs(records)
}

func (c customizeCareTypeRepository) Init(ctx context.Context, companyId int64, staffId int64,
	isAllowBoardingAndDaycare bool, isAllowDogWalking bool, isAllowGroupClass bool) ([]*do.CustomizeCareTypeDO, error) {
	// 幂等性检查
	var count int64
	if err := c.db.WithContext(ctx).
		Model(&models.CustomizeCareType{}).
		Where("company_id = ?", companyId).
		Count(&count).Error; err != nil {
		return nil, errors.Wrapf(err, "failed to check existing care types")
	}

	if count > 0 {
		return nil, errors.Errorf("care types already initialized for company %d", companyId)
	}

	// 初始化数据
	records := make([]*models.CustomizeCareType, 0)
	recordTime := time.Now()

	// init Grooming
	name := offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_GROOMING)]
	records = append(records, toCustomizeCareType(name, companyId, offeringpb.ServiceItemType_GROOMING, staffId, recordTime))

	// is in Boarding/Daycare/Evaluation white list
	if isAllowBoardingAndDaycare {
		for _, serviceType := range initBoardingAndDaycareTypes {
			name := offeringpb.ServiceItemType_name[int32(serviceType)]
			records = append(records, toCustomizeCareType(name, companyId, serviceType, staffId, recordTime))
		}
	}

	// is in GroupClass white list
	if isAllowGroupClass {
		name := offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_GROUP_CLASS)]
		records = append(records, toCustomizeCareType(name, companyId, offeringpb.ServiceItemType_GROUP_CLASS, staffId, recordTime))
	}

	// is in DogWalking white list
	if isAllowDogWalking {
		name := offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_DOG_WALKING)]
		records = append(records, toCustomizeCareType(name, companyId, offeringpb.ServiceItemType_DOG_WALKING, staffId, recordTime))
	}

	// 事务处理
	err := c.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(records, 50).Error; err != nil {
			return errors.Wrapf(err, "failed to create initial care types")
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	dos, err := converter.ConvertCustomizeCareTypesToDOs(records)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to convert care types to DOs")
	}
	return dos, nil
}

func toCustomizeCareType(
	name string, companyId int64, serviceType offeringpb.ServiceItemType, staffId int64, recordTime time.Time) *models.CustomizeCareType {
	return &models.CustomizeCareType{
		Name:            FormatEnumString(name),
		CompanyID:       companyId,
		ServiceItemType: serviceType,
		CreatedAt:       &recordTime,
		UpdatedAt:       &recordTime,
		UpdatedBy:       staffId,
		Sort:            InitSort(serviceType),
	}
}

var initBoardingAndDaycareTypes = []offeringpb.ServiceItemType{
	offeringpb.ServiceItemType_BOARDING,
	offeringpb.ServiceItemType_DAYCARE,
	offeringpb.ServiceItemType_EVALUATION,
}

func InitSort(itemType offeringpb.ServiceItemType) int32 {
	switch itemType {
	case offeringpb.ServiceItemType_BOARDING:
		return 1
	case offeringpb.ServiceItemType_DAYCARE:
		return 2
	case offeringpb.ServiceItemType_GROOMING:
		return 3
	case offeringpb.ServiceItemType_DOG_WALKING:
		return 4
	case offeringpb.ServiceItemType_GROUP_CLASS:
		return 5
	case offeringpb.ServiceItemType_EVALUATION:
		return 6
	default:
		return 0
	}
}

// IsNameDuplicate 检查名称是否重复
func (c customizeCareTypeRepository) IsNameDuplicate(ctx context.Context, companyId int64, name string, excludeId *int64) (bool, error) {
	if name == "" {
		return false, nil
	}

	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		return false, nil
	}

	var existing []models.CustomizeCareType
	query := c.db.WithContext(ctx).
		Where("company_id = ?", companyId).
		Where("LOWER(TRIM(name)) = LOWER(?)", trimmedName)

	if excludeId != nil {
		query = query.Where("id != ?", *excludeId)
	}

	if err := query.Find(&existing).Error; err != nil {
		return false, errors.Wrap(err, "failed to check name duplicate")
	}

	return len(existing) > 0, nil
}

func FormatEnumString(s string) string {
	parts := strings.Split(s, "_")
	for i, part := range parts {
		if len(part) == 0 {
			continue
		}
		runes := []rune(strings.ToLower(part))
		runes[0] = unicode.ToUpper(runes[0])
		parts[i] = string(runes)
	}
	return strings.Join(parts, " ")
}
