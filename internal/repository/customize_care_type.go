package repository

import (
	"context"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gormx"
	"sort"
	"strings"
	"time"
	"unicode"
)

type CustomizeCareTypeRepository interface {
	List(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error)
	Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error
	IsNameDuplicate(ctx context.Context, companyId int64, name string, serviceItemType offeringpb.ServiceItemType, filter *offeringsvcpb.WhiteListFilter) (bool, error)
}

type customizeCareTypeRepository struct {
	db *gorm.DB
}

func NewCustomizeCareTypeRepository(db *gorm.DB) CustomizeCareTypeRepository {
	return &customizeCareTypeRepository{
		db: db,
	}
}

func (c customizeCareTypeRepository) Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error {
	if whereOpt == nil || updateOpt == nil {
		return errors.New("whereOpt and updateOpt cannot be nil")
	}
	whereQuery := gormx.Query(whereOpt)
	if whereQuery == nil {
		return errors.New("empty where condition is not allowed")
	}
	if err := c.db.WithContext(ctx).Model(&models.CustomizeCareType{}).Where(gormx.Query(whereOpt)).Updates(gormx.Update(updateOpt)).Error; err != nil {
		return errors.Wrap(err, "failed to update customize care types")
	}
	return nil
}

func (c customizeCareTypeRepository) List(
	ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error) {
	if whereOpt.CompanyID == nil {
		return nil, errors.New("companyId cannot be nil")
	}
	// build default records
	records := buildDefaultCareTypeDoList(*whereOpt.CompanyID, filter)
	// override records
	query := c.db.WithContext(ctx).Where("company_id = ?", *whereOpt.CompanyID).Order("sort ASC")

	overrideRecords := make([]*models.CustomizeCareType, 0)
	if err := query.Find(&overrideRecords).Error; err != nil {
		return nil, errors.WithStack(err)
	}

	overrideMap := make(map[offeringpb.ServiceItemType]string)
	for _, o := range overrideRecords {
		overrideMap[o.ServiceItemType] = o.Name
	}
	for _, record := range records {
		if name, ok := overrideMap[record.ServiceItemType]; ok {
			record.Name = name
		}
	}

	sort.Slice(records, func(i, j int) bool {
		return records[i].Sort < records[j].Sort
	})

	return records, nil
}

func buildDefaultCareTypeDoList(companyId int64, filter *offeringsvcpb.WhiteListFilter) []*do.CustomizeCareTypeDO {
	records := make([]*do.CustomizeCareTypeDO, 0)

	// Grooming
	name := FormatEnumString(offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_GROOMING)])
	records = append(records, toCustomizeCareTypeDo(name, companyId, offeringpb.ServiceItemType_GROOMING))

	// is in Boarding/Daycare/Evaluation white list
	if filter.IsAllowBoardingAndDaycare {
		for _, serviceType := range initBoardingAndDaycareTypes {
			name := FormatEnumString(offeringpb.ServiceItemType_name[int32(serviceType)])
			records = append(records, toCustomizeCareTypeDo(name, companyId, serviceType))
		}
	}

	// is in GroupClass white list
	if filter.IsAllowGroupClass {
		name := FormatEnumString(offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_GROUP_CLASS)])
		records = append(records, toCustomizeCareTypeDo(name, companyId, offeringpb.ServiceItemType_GROUP_CLASS))
	}

	// is in DogWalking white list
	if filter.IsAllowDogWalking {
		name := FormatEnumString(offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_DOG_WALKING)])
		records = append(records, toCustomizeCareTypeDo(name, companyId, offeringpb.ServiceItemType_DOG_WALKING))
	}
	return records
}

func toCustomizeCareTypeDo(name string, companyId int64, serviceItemType offeringpb.ServiceItemType) *do.CustomizeCareTypeDO {
	return &do.CustomizeCareTypeDO{
		Name:            name,
		CompanyID:       companyId,
		ServiceItemType: serviceItemType,
		Sort:            InitSort(serviceItemType),
	}
}

func toCustomizeCareType(
	name string, companyId int64, serviceItemType offeringpb.ServiceItemType, staffId int64, recordTime time.Time) *models.CustomizeCareType {
	return &models.CustomizeCareType{
		Name:            FormatEnumString(name),
		CompanyID:       companyId,
		ServiceItemType: serviceItemType,
		CreatedAt:       &recordTime,
		UpdatedAt:       &recordTime,
		UpdatedBy:       staffId,
		Sort:            InitSort(serviceItemType),
	}
}

var initBoardingAndDaycareTypes = []offeringpb.ServiceItemType{
	offeringpb.ServiceItemType_BOARDING,
	offeringpb.ServiceItemType_DAYCARE,
	offeringpb.ServiceItemType_EVALUATION,
}

func InitSort(itemType offeringpb.ServiceItemType) int32 {
	switch itemType {
	case offeringpb.ServiceItemType_BOARDING:
		return 1
	case offeringpb.ServiceItemType_DAYCARE:
		return 2
	case offeringpb.ServiceItemType_GROOMING:
		return 3
	case offeringpb.ServiceItemType_DOG_WALKING:
		return 4
	case offeringpb.ServiceItemType_GROUP_CLASS:
		return 5
	case offeringpb.ServiceItemType_EVALUATION:
		return 6
	default:
		return 0
	}
}

// IsNameDuplicate 检查名称是否重复
func (c customizeCareTypeRepository) IsNameDuplicate(
	ctx context.Context, companyId int64, name string, serviceItemType offeringpb.ServiceItemType, filter *offeringsvcpb.WhiteListFilter) (bool, error) {
	if name == "" {
		return false, errors.New("name cannot be empty")
	}

	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		return false, errors.New("name cannot be empty")
	}

	var existing []models.CustomizeCareType
	list, err := c.List(ctx, &models.CustomizeCareTypeWhereOpt{CompanyID: &companyId}, filter)
	if err != nil {
		return false, errors.Wrap(err, "failed to list customize care types")
	}

	query := c.db.WithContext(ctx).
		Where("company_id = ?", companyId).
		Where("LOWER(TRIM(name)) = LOWER(?)", trimmedName)

	if serviceItemType.Number() != 0 {
		query = query.Where("service_item_type != ?", serviceItemType.Number())
	}

	if err := query.Find(&existing).Error; err != nil {
		return false, errors.Wrap(err, "failed to check name duplicate")
	}

	return len(existing) > 0, nil
}

func FormatEnumString(s string) string {
	parts := strings.Split(s, "_")
	for i, part := range parts {
		if len(part) == 0 {
			continue
		}
		runes := []rune(strings.ToLower(part))
		runes[0] = unicode.ToUpper(runes[0])
		parts[i] = string(runes)
	}
	return strings.Join(parts, " ")
}
