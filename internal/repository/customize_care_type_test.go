package repository

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	err = db.AutoMigrate(&models.CustomizeCareType{})
	assert.NoError(t, err)

	return db
}

func TestCustomizeCareTypeRepository_IsNameDuplicate(t *testing.T) {
	db := setupTestDB(t)
	repo := NewCustomizeCareTypeRepository(db)
	ctx := context.Background()

	// 创建测试数据
	testData := &models.CustomizeCareType{
		ID:              1,
		Name:            "Test Care Type",
		CompanyID:       100,
		ServiceItemType: offeringpb.ServiceItemType_GROOMING,
		Sort:            1,
		UpdatedBy:       1,
	}
	err := db.Create(testData).Error
	assert.NoError(t, err)

	tests := []struct {
		name      string
		companyId int64
		inputName string
		excludeId *int64
		expected  bool
		wantErr   bool
	}{
		{
			name:      "exact match - should be duplicate",
			companyId: 100,
			inputName: "Test Care Type",
			excludeId: nil,
			expected:  true,
			wantErr:   false,
		},
		{
			name:      "case insensitive match - should be duplicate",
			companyId: 100,
			inputName: "test care type",
			excludeId: nil,
			expected:  true,
			wantErr:   false,
		},
		{
			name:      "with extra spaces - should be duplicate",
			companyId: 100,
			inputName: "  Test Care Type  ",
			excludeId: nil,
			expected:  true,
			wantErr:   false,
		},
		{
			name:      "different company - should not be duplicate",
			companyId: 200,
			inputName: "Test Care Type",
			excludeId: nil,
			expected:  false,
			wantErr:   false,
		},
		{
			name:      "exclude current id - should not be duplicate",
			companyId: 100,
			inputName: "Test Care Type",
			excludeId: &testData.ID,
			expected:  false,
			wantErr:   false,
		},
		{
			name:      "different name - should not be duplicate",
			companyId: 100,
			inputName: "Different Care Type",
			excludeId: nil,
			expected:  false,
			wantErr:   false,
		},
		{
			name:      "empty name - should return error",
			companyId: 100,
			inputName: "",
			excludeId: nil,
			expected:  false,
			wantErr:   true,
		},
		{
			name:      "whitespace only name - should return error",
			companyId: 100,
			inputName: "   ",
			excludeId: nil,
			expected:  false,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := repo.IsNameDuplicate(ctx, tt.companyId, tt.inputName, tt.excludeId)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}
