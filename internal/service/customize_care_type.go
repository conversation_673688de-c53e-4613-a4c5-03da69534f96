package service

import (
	"context"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"time"
)

type CustomizeCareTypeHandler interface {
	List(ctx context.Context, companyId int64, serviceItemType []offeringpb.ServiceItemType) ([]*do.CustomizeCareTypeDO, error)
	Update(ctx context.Context, id int64, companyId int64, name string, staffId int64) error
	Init(ctx context.Context, companyId int64, staffId int64,
		isAllowBoardingAndDaycare bool, isAllowDogWalking bool, isAllowGroupClass bool) ([]*do.CustomizeCareTypeDO, error)
}

type customizeCareTypeHandler struct {
	companyClient               clients.CompanyClient
	customizeCareTypeRepository repository.CustomizeCareTypeRepository
}

func NewCustomizeCareTypeHandler(customizeCareTypeRepository repository.CustomizeCareTypeRepository) CustomizeCareTypeHandler {
	return &customizeCareTypeHandler{
		companyClient:               clients.NewCompanyClient(resource.GetCompanyServiceClient()),
		customizeCareTypeRepository: customizeCareTypeRepository,
	}
}

func (c customizeCareTypeHandler) List(ctx context.Context, companyId int64, serviceItemType []offeringpb.ServiceItemType) ([]*do.CustomizeCareTypeDO, error) {
	opt := &models.CustomizeCareTypeWhereOpt{
		CompanyID: &companyId,
	}
	if len(serviceItemType) > 0 {
		opt.ServiceItemTypes = serviceItemType
	}
	return c.customizeCareTypeRepository.List(ctx, opt)
}

func (c customizeCareTypeHandler) Update(ctx context.Context, id int64, companyId int64, name string, staffId int64) error {
	// 检查名称是否重复
	isDuplicate, err := c.customizeCareTypeRepository.IsNameDuplicate(ctx, companyId, name, &id)
	if err != nil {
		return status.Errorf(codes.Internal, "failed to check name duplicate: %v", err)
	}
	if isDuplicate {
		return status.Errorf(codes.AlreadyExists, "care type name '%s' already exists", name)
	}

	whereOpt := &models.CustomizeCareTypeWhereOpt{
		ID:        &id,
		CompanyID: &companyId,
	}
	updateOpt := &models.CustomizeCareTypeUpdateOpt{
		Name:      &name,
		UpdatedBy: &staffId,
		UpdatedAt: time.Now(),
	}
	return c.customizeCareTypeRepository.Update(ctx, whereOpt, updateOpt)
}

func (c customizeCareTypeHandler) Init(ctx context.Context, companyId int64, staffId int64,
	isAllowBoardingAndDaycare bool, isAllowDogWalking bool, isAllowGroupClass bool) ([]*do.CustomizeCareTypeDO, error) {
	return c.customizeCareTypeRepository.Init(ctx, companyId, staffId, isAllowBoardingAndDaycare, isAllowDogWalking, isAllowGroupClass)
}
