package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

// MockCustomizeCareTypeRepository is a mock implementation of CustomizeCareTypeRepository
type MockCustomizeCareTypeRepository struct {
	mock.Mock
}

func (m *MockCustomizeCareTypeRepository) List(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt) ([]*do.CustomizeCareTypeDO, error) {
	args := m.Called(ctx, whereOpt)
	return args.Get(0).([]*do.CustomizeCareTypeDO), args.Error(1)
}

func (m *MockCustomizeCareTypeRepository) Init(ctx context.Context, companyId int64, staffId int64, isAllowBoardingAndDaycare bool, isAllowDogWalking bool, isAllowGroupClass bool) ([]*do.CustomizeCareTypeDO, error) {
	args := m.Called(ctx, companyId, staffId, isAllowBoardingAndDaycare, isAllowDogWalking, isAllowGroupClass)
	return args.Get(0).([]*do.CustomizeCareTypeDO), args.Error(1)
}

func (m *MockCustomizeCareTypeRepository) Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error {
	args := m.Called(ctx, whereOpt, updateOpt)
	return args.Error(0)
}

func (m *MockCustomizeCareTypeRepository) IsNameDuplicate(ctx context.Context, companyId int64, name string, excludeId *int64) (bool, error) {
	args := m.Called(ctx, companyId, name, excludeId)
	return args.Bool(0), args.Error(1)
}

func TestCustomizeCareTypeHandler_Update(t *testing.T) {
	tests := []struct {
		name           string
		id             int64
		companyId      int64
		inputName      string
		staffId        int64
		isDuplicate    bool
		duplicateError error
		updateError    error
		expectedError  string
		expectedCode   codes.Code
	}{
		{
			name:           "successful update",
			id:             1,
			companyId:      100,
			inputName:      "New Care Type",
			staffId:        1,
			isDuplicate:    false,
			duplicateError: nil,
			updateError:    nil,
			expectedError:  "",
			expectedCode:   codes.OK,
		},
		{
			name:           "duplicate name error",
			id:             1,
			companyId:      100,
			inputName:      "Existing Care Type",
			staffId:        1,
			isDuplicate:    true,
			duplicateError: nil,
			updateError:    nil,
			expectedError:  "care type name 'Existing Care Type' already exists",
			expectedCode:   codes.AlreadyExists,
		},
		{
			name:           "database error during duplicate check",
			id:             1,
			companyId:      100,
			inputName:      "Some Care Type",
			staffId:        1,
			isDuplicate:    false,
			duplicateError: assert.AnError,
			updateError:    nil,
			expectedError:  "failed to check name duplicate",
			expectedCode:   codes.Internal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock
			mockRepo := new(MockCustomizeCareTypeRepository)
			handler := &customizeCareTypeHandler{
				customizeCareTypeRepository: mockRepo,
			}

			ctx := context.Background()

			// Setup expectations
			mockRepo.On("IsNameDuplicate", ctx, tt.companyId, tt.inputName, &tt.id).
				Return(tt.isDuplicate, tt.duplicateError)

			if tt.duplicateError == nil && !tt.isDuplicate {
				mockRepo.On("Update", ctx, mock.AnythingOfType("*models.CustomizeCareTypeWhereOpt"), mock.AnythingOfType("*models.CustomizeCareTypeUpdateOpt")).
					Return(tt.updateError)
			}

			// Execute
			err := handler.Update(ctx, tt.id, tt.companyId, tt.inputName, tt.staffId)

			// Assert
			if tt.expectedError == "" {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				
				// Check gRPC status code
				if st, ok := status.FromError(err); ok {
					assert.Equal(t, tt.expectedCode, st.Code())
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}
