package engine

import (
	"fmt"
	"sort"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

// PricingEngine defines the rule engine that evaluates and applies pricing rules
type PricingEngine struct {
	Rules         []do.PricingRuleRecordDO
	ApplyBestOnly bool
	ApplySequence []offeringModelsV2.RuleType

	multiPetRule  PricingRule
	multiStayRule PricingRule
	peakDateRule  PricingRule
}

type ServiceRule struct {
	ServiceId           int64
	MultiPetRuleRecord  do.PricingRuleRecordDO
	MultiStayRuleRecord do.PricingRuleRecordDO
	PeakDateRuleRecords []do.PricingRuleRecordDO
}

// NewPricingEngine creates a new pricing engine with the given rules
func NewPricingEngine(rules []do.PricingRuleRecordDO, applyBestOnly bool, applySequence []offeringModelsV2.RuleType) *PricingEngine {
	return &PricingEngine{
		Rules:         rules,
		ApplyBestOnly: applyBestOnly,
		ApplySequence: applySequence,

		multiPetRule:  NewMultiPetRule(),
		multiStayRule: NewMultiStayRule(),
		peakDateRule:  NewPeakDateRule(),
	}
}

func uniqueByServiceIdAndType(details []*do.CalculateDO) []*do.CalculateDO {
	return lo.UniqBy(details, func(detail *do.CalculateDO) string {
		return fmt.Sprintf("%d:%s", detail.ServiceId, detail.ServiceItemType)
	})
}

func groupRulesByServiceId(details []*do.CalculateDO, rules []do.PricingRuleRecordDO) map[int64]ServiceRule {
	groupedRules := make(map[int64]ServiceRule)

	for _, detail := range uniqueByServiceIdAndType(details) {
		serviceId := detail.ServiceId
		careType := detail.ServiceItemType
		if careType != offeringModelsV1.ServiceItemType_BOARDING && careType != offeringModelsV1.ServiceItemType_DAYCARE {
			continue
		}
		groupedRules[serviceId] = ServiceRule{
			ServiceId: serviceId,
		}
		for _, rule := range rules {
			if (rule.AllBoardingApplicable && careType == offeringModelsV1.ServiceItemType_BOARDING) ||
				(rule.AllDaycareApplicable && careType == offeringModelsV1.ServiceItemType_DAYCARE) ||
				lo.Contains(rule.SelectedBoardingServices, serviceId) || lo.Contains(rule.SelectedDaycareServices, serviceId) {

				serviceRule := groupedRules[serviceId]

				switch rule.RuleType {
				case offeringpb.RuleType_MULTIPLE_PET:
					serviceRule.MultiPetRuleRecord = rule
				case offeringpb.RuleType_MULTIPLE_STAY:
					serviceRule.MultiStayRuleRecord = rule
				case offeringpb.RuleType_PEAK_DATE:
					serviceRule.PeakDateRuleRecords = append(serviceRule.PeakDateRuleRecords, rule)
				default:
					break
				}

				groupedRules[serviceId] = serviceRule
			}
		}
	}

	return groupedRules
}

// CalculatePrice calculates the price for an appointment
func (e *PricingEngine) CalculatePrice(petDetails []*do.CalculateDO) []*do.CalculateDO {
	allPetDetails := e.initializeResults(petDetails)
	serviceIdRule := groupRulesByServiceId(allPetDetails, e.Rules)

	if e.ApplyBestOnly {
		// ApplyBestOnly模式：分两阶段处理，避免重复更新allPetDetails
		e.applyBestOnlyRules(allPetDetails, serviceIdRule)
	} else {
		// 顺序应用模式：按照ApplySequence顺序应用规则
		e.applySequentialRules(allPetDetails, serviceIdRule)
	}

	return allPetDetails
}

func (e *PricingEngine) CalculateFormula(petDetails []*do.CalculateDO, symbol string) string {
	details := e.initializeResults(petDetails)
	serviceIdRule := groupRulesByServiceId(details, e.Rules)

	for _, serviceRule := range serviceIdRule {
		serviceId := serviceRule.ServiceId
		multiPet := serviceRule.MultiPetRuleRecord
		multiStay := serviceRule.MultiStayRuleRecord

		currentServiceDetails := lo.Filter(details, func(detail *do.CalculateDO, _ int) bool {
			return detail.ServiceId == serviceId
		})

		formula := fmt.Sprintf("%s%s", symbol, currentServiceDetails[0].ServicePrice.StringFixed(2))

		// multi pet, multi stay
		if e.ApplyBestOnly {
			// 计算多宠物规则的折扣
			multiPetResults := cloneResults(currentServiceDetails)
			e.multiPetRule.Apply(multiPetResults, multiPet)

			// 计算多住宿规则的折扣
			multiStayResults := cloneResults(currentServiceDetails)
			e.multiStayRule.Apply(multiStayResults, multiStay)

			// 比较总折扣
			petDiscount := calculatePriceDifference(multiPetResults, serviceId, multiPet.ID)
			stayDiscount := calculatePriceDifference(multiStayResults, serviceId, multiStay.ID)

			// 应用折扣更好的结果
			if petDiscount.GreaterThan(stayDiscount) {
				adjustedPrice := getAdjustedPrice(multiPetResults)
				formula = e.multiPetRule.AppendFormula(currentServiceDetails, multiPet, symbol, formula) + " = " + fmt.Sprintf("%s%s", symbol, adjustedPrice.StringFixed(2))
			} else {
				adjustedPrice := getAdjustedPrice(multiStayResults)
				formula = e.multiStayRule.AppendFormula(currentServiceDetails, multiStay, symbol, formula) + " = " + fmt.Sprintf("%s%s", symbol, adjustedPrice.StringFixed(2))
			}

			return formula
		} else {
			for _, ruleType := range e.ApplySequence {
				if ruleType == offeringpb.RuleType_MULTIPLE_PET {
					e.multiPetRule.Apply(currentServiceDetails, multiPet)
					formula = e.multiPetRule.AppendFormula(currentServiceDetails, multiPet, symbol, formula)
				} else if ruleType == offeringpb.RuleType_MULTIPLE_STAY {
					e.multiStayRule.Apply(currentServiceDetails, multiStay)
					formula = e.multiStayRule.AppendFormula(currentServiceDetails, multiStay, symbol, formula)
				}
			}

			// 获取最终价格
			adjustedPrice := getAdjustedPrice(currentServiceDetails)
			return formula + " = " + fmt.Sprintf("%s%s", symbol, adjustedPrice.StringFixed(2))
		}

	}

	return ""
}

func updateFinalResults(results []*do.CalculateDO, details []*do.CalculateDO) {
	for _, detail := range details {
		for _, result := range results {
			if result.PetId == detail.PetId &&
				result.ServiceId == detail.ServiceId &&
				result.ServiceDate == detail.ServiceDate {
				detail.AdjustedPrice = result.AdjustedPrice
				detail.IsHitPricingRule = result.IsHitPricingRule
				detail.UsedPricingRuleIds = result.UsedPricingRuleIds
				detail.PricingRuleIdPriceMap = result.PricingRuleIdPriceMap
			}
		}
	}
}

func cloneResults(details []*do.CalculateDO) []*do.CalculateDO {
	results := make([]*do.CalculateDO, len(details))
	for i, petDetail := range details {
		results[i] = &do.CalculateDO{
			PetId:                 petDetail.PetId,
			ServiceId:             petDetail.ServiceId,
			ServicePrice:          petDetail.ServicePrice,
			LodgingUnitId:         petDetail.LodgingUnitId,
			ScopeTypePrice:        petDetail.ScopeTypePrice,
			ServiceDate:           petDetail.ServiceDate,
			IsHitPricingRule:      false,
			UsedPricingRuleIds:    make([]int64, 0),
			ServiceItemType:       petDetail.ServiceItemType,
			AdjustedPrice:         petDetail.AdjustedPrice,
			PricingRuleIdPriceMap: cloneDecimalMap(petDetail.PricingRuleIdPriceMap),
		}
	}
	return results
}

func (e *PricingEngine) initializeResults(details []*do.CalculateDO) []*do.CalculateDO {
	results := make([]*do.CalculateDO, len(details))
	for i, petDetail := range details {
		results[i] = &do.CalculateDO{
			PetId:                 petDetail.PetId,
			ServiceId:             petDetail.ServiceId,
			ServicePrice:          petDetail.ServicePrice,
			LodgingUnitId:         petDetail.LodgingUnitId,
			ScopeTypePrice:        petDetail.ScopeTypePrice,
			ServiceDate:           petDetail.ServiceDate,
			IsHitPricingRule:      false,
			UsedPricingRuleIds:    make([]int64, 0),
			ServiceItemType:       petDetail.ServiceItemType,
			AdjustedPrice:         petDetail.ServicePrice.RoundBank(2),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		}
	}

	// sort by price (descending) and then by pet id
	sort.Slice(results, func(i, j int) bool {
		// If prices are different, sort by price descending
		if !results[i].ServicePrice.Equal(results[j].ServicePrice) {
			return results[i].ServicePrice.GreaterThan(results[j].ServicePrice)
		}

		// If prices are equal, sort by pet id descending
		return results[i].PetId < results[j].PetId
	})

	return results
}

// 计算当前 service 的总折扣
func calculatePriceDifference(results []*do.CalculateDO, serviceId int64, ruleId *int64) decimal.Decimal {
	adjustedPrice := decimal.Zero
	for _, result := range results {
		if result.ServiceId != serviceId || ruleId == nil {
			continue
		}
		value, ok := result.PricingRuleIdPriceMap[*ruleId]
		if ok {
			adjustedPrice = adjustedPrice.Add(value)
		}
	}

	return adjustedPrice
}

func cloneDecimalMap(original map[int64]decimal.Decimal) map[int64]decimal.Decimal {
	clone := make(map[int64]decimal.Decimal, len(original))

	for key, value := range original {
		clone[key] = value
	}

	return clone
}
