package engine

import (
	"fmt"
	"sort"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

// PricingEngine defines the rule engine that evaluates and applies pricing rules
type PricingEngine struct {
	Rules         []do.PricingRuleRecordDO
	ApplyBestOnly bool
	ApplySequence []offeringModelsV2.RuleType

	multiPetRule  PricingRule
	multiStayRule PricingRule
	peakDateRule  PricingRule
}

type ServiceRule struct {
	ServiceId           int64
	MultiPetRuleRecord  do.PricingRuleRecordDO
	MultiStayRuleRecord do.PricingRuleRecordDO
	PeakDateRuleRecords []do.PricingRuleRecordDO
}

// NewPricingEngine creates a new pricing engine with the given rules
func NewPricingEngine(rules []do.PricingRuleRecordDO, applyBestOnly bool, applySequence []offeringModelsV2.RuleType) *PricingEngine {
	return &PricingEngine{
		Rules:         rules,
		ApplyBestOnly: applyBestOnly,
		ApplySequence: applySequence,

		multiPetRule:  NewMultiPetRule(),
		multiStayRule: NewMultiStayRule(),
		peakDateRule:  NewPeakDateRule(),
	}
}

func uniqueByServiceIdAndType(details []*do.CalculateDO) []*do.CalculateDO {
	return lo.UniqBy(details, func(detail *do.CalculateDO) string {
		return fmt.Sprintf("%d:%s", detail.ServiceId, detail.ServiceItemType)
	})
}

func groupRulesByServiceId(details []*do.CalculateDO, rules []do.PricingRuleRecordDO) map[int64]ServiceRule {
	groupedRules := make(map[int64]ServiceRule)

	for _, detail := range uniqueByServiceIdAndType(details) {
		serviceId := detail.ServiceId
		careType := detail.ServiceItemType
		if careType != offeringModelsV1.ServiceItemType_BOARDING && careType != offeringModelsV1.ServiceItemType_DAYCARE {
			continue
		}
		groupedRules[serviceId] = ServiceRule{
			ServiceId: serviceId,
		}
		for _, rule := range rules {
			if (rule.AllBoardingApplicable && careType == offeringModelsV1.ServiceItemType_BOARDING) ||
				(rule.AllDaycareApplicable && careType == offeringModelsV1.ServiceItemType_DAYCARE) ||
				lo.Contains(rule.SelectedBoardingServices, serviceId) || lo.Contains(rule.SelectedDaycareServices, serviceId) {

				serviceRule := groupedRules[serviceId]

				switch rule.RuleType {
				case offeringpb.RuleType_MULTIPLE_PET:
					serviceRule.MultiPetRuleRecord = rule
				case offeringpb.RuleType_MULTIPLE_STAY:
					serviceRule.MultiStayRuleRecord = rule
				case offeringpb.RuleType_PEAK_DATE:
					serviceRule.PeakDateRuleRecords = append(serviceRule.PeakDateRuleRecords, rule)
				default:
					break
				}

				groupedRules[serviceId] = serviceRule
			}
		}
	}

	return groupedRules
}

// CalculatePrice calculates the price for an appointment
func (e *PricingEngine) CalculatePrice(petDetails []*do.CalculateDO) []*do.CalculateDO {
	allPetDetails := e.initializeResults(petDetails)
	serviceIdRule := groupRulesByServiceId(allPetDetails, e.Rules)

	if e.ApplyBestOnly {
		// ApplyBestOnly模式：分两阶段处理，避免重复更新allPetDetails
		e.applyBestOnlyRules(allPetDetails, serviceIdRule)
	} else {
		// ApplySequential模式：按照ApplySequence顺序应用规则
		e.applySequentialRules(allPetDetails, serviceIdRule)
	}

	return allPetDetails
}

type ServiceBestRule struct {
	ServiceId   int64
	BestResults []*do.CalculateDO
	RuleType    offeringpb.RuleType
}

// applyBestOnlyRules 应用最优规则模式
func (e *PricingEngine) applyBestOnlyRules(allPetDetails []*do.CalculateDO, serviceIdRule map[int64]ServiceRule) {
	// 第一阶段：收集每个serviceId的最优规则选择
	serviceBestRules := make([]ServiceBestRule, 0)

	for _, serviceRule := range serviceIdRule {
		serviceId := serviceRule.ServiceId
		multiPet := serviceRule.MultiPetRuleRecord
		multiStay := serviceRule.MultiStayRuleRecord

		currentServiceDetails := lo.Filter(allPetDetails, func(detail *do.CalculateDO, _ int) bool {
			return detail.ServiceId == serviceId
		})

		// 计算多宠物规则的折扣（必须使用所有的 pet detail 数据，用于计算同 rule 下的 pet 数量）
		multiPetResults := cloneResults(allPetDetails)
		e.multiPetRule.Apply(multiPetResults, multiPet)

		// 计算多住宿规则的折扣
		multiStayResults := cloneResults(currentServiceDetails)
		e.multiStayRule.Apply(multiStayResults, multiStay)

		// 比较总折扣
		petDiscount := calculatePriceDifference(multiPetResults, serviceId, multiPet.ID)
		stayDiscount := calculatePriceDifference(multiStayResults, serviceId, multiStay.ID)

		// 选择折扣更好的结果
		if stayDiscount.GreaterThan(petDiscount) {
			serviceBestRules = append(serviceBestRules, ServiceBestRule{
				ServiceId:   serviceId,
				BestResults: multiStayResults,
				RuleType:    offeringpb.RuleType_MULTIPLE_STAY,
			})
		} else {
			serviceBestRules = append(serviceBestRules, ServiceBestRule{
				ServiceId:   serviceId,
				BestResults: multiPetResults,
				RuleType:    offeringpb.RuleType_MULTIPLE_PET,
			})
		}
	}

	// 第二阶段：统一应用所有最优规则
	e.applyCollectedBestRules(allPetDetails, serviceBestRules, serviceIdRule)
}

// applySequentialRules 应用顺序规则模式
func (e *PricingEngine) applySequentialRules(allPetDetails []*do.CalculateDO, serviceIdRule map[int64]ServiceRule) {
	// 第一阶段：按优先级应用全局规则（multi pet）和服务级规则（multi stay）
	for _, ruleType := range e.ApplySequence {
		switch ruleType {
		case offeringpb.RuleType_MULTIPLE_PET:
			// 全局规则：只执行一次
			e.applyGlobalMultiPetRule(allPetDetails, serviceIdRule)
		case offeringpb.RuleType_MULTIPLE_STAY:
			// 服务级规则：对每个service执行
			e.applyMultiStayRuleForAllServices(allPetDetails, serviceIdRule)
		}
	}

	// 第二阶段：应用peak date规则（最后执行，无优先级冲突）
	e.applyPeakDateRuleForAllServices(allPetDetails, serviceIdRule)
}

func (e *PricingEngine) applyGlobalMultiPetRule(allPetDetails []*do.CalculateDO, serviceIdRule map[int64]ServiceRule) {
	// 找到第一个有效的multi-pet配置
	for _, serviceRule := range serviceIdRule {
		if serviceRule.MultiPetRuleRecord.ID != nil {
			e.multiPetRule.Apply(allPetDetails, serviceRule.MultiPetRuleRecord)
			break // 只执行一次
		}
	}
}

func (e *PricingEngine) applyMultiStayRuleForAllServices(allPetDetails []*do.CalculateDO, serviceIdRule map[int64]ServiceRule) {
	for _, serviceRule := range serviceIdRule {
		currentServiceDetails := lo.Filter(allPetDetails, func(detail *do.CalculateDO, _ int) bool {
			return detail.ServiceId == serviceRule.ServiceId
		})
		e.multiStayRule.Apply(currentServiceDetails, serviceRule.MultiStayRuleRecord)
	}
}

func (e *PricingEngine) applyPeakDateRuleForAllServices(allPetDetails []*do.CalculateDO, serviceIdRule map[int64]ServiceRule) {
	for _, serviceRule := range serviceIdRule {
		currentServiceDetails := lo.Filter(allPetDetails, func(detail *do.CalculateDO, _ int) bool {
			return detail.ServiceId == serviceRule.ServiceId
		})
		// 以 pet_id+service_id+service_date 作为唯一键，计算最佳 surcharge
		bestResultMap := make(map[string]*do.CalculateDO)
		for _, detail := range currentServiceDetails {
			key := getCalculateKey(detail)
			bestResultMap[key] = detail
		}

		peakDates := serviceRule.PeakDateRuleRecords
		for _, peakDate := range peakDates {
			peakDateResults := cloneResults(currentServiceDetails)
			e.peakDateRule.Apply(peakDateResults, peakDate)

			for _, currentResult := range peakDateResults {
				key := getCalculateKey(currentResult)
				bestResult := bestResultMap[key]
				betterResult := selectBetterCalculateResult(currentResult, bestResult)
				bestResultMap[key] = betterResult
			}
		}

		// 同步回allPetDetails
		for key, bestResult := range bestResultMap {
			for i, detail := range allPetDetails {
				if getCalculateKey(detail) == key {
					allPetDetails[i] = bestResult
					break
				}
			}
		}
	}
}

// applyCollectedBestRules 应用收集到的最优规则
func (e *PricingEngine) applyCollectedBestRules(allPetDetails []*do.CalculateDO, serviceBestRules []ServiceBestRule, serviceIdRule map[int64]ServiceRule) {
	// 收集所有选择了多宠物规则的serviceId
	multiPetServiceIds := make([]int64, 0)
	multiStayRules := make([]ServiceBestRule, 0)

	for _, bestRule := range serviceBestRules {
		switch bestRule.RuleType {
		case offeringpb.RuleType_MULTIPLE_PET:
			multiPetServiceIds = append(multiPetServiceIds, bestRule.ServiceId)
		case offeringpb.RuleType_MULTIPLE_STAY:
			multiStayRules = append(multiStayRules, bestRule)
		}
	}

	// 如果有serviceId选择了多宠物规则，统一应用多宠物规则
	if len(multiPetServiceIds) > 0 {
		// 找到第一个多宠物规则并应用到所有宠物
		for _, bestRule := range serviceBestRules {
			if bestRule.RuleType == offeringpb.RuleType_MULTIPLE_PET {
				// 多宠物规则影响所有宠物
				updateFinalResults(bestRule.BestResults, allPetDetails)
				break
			}
		}
	}

	// 应用多住宿规则（只对没有选择多宠物规则的serviceId）
	for _, multiStayRule := range multiStayRules {
		// 检查这个serviceId是否已经被多宠物规则处理过
		if !lo.Contains(multiPetServiceIds, multiStayRule.ServiceId) {
			updateFinalResultsForService(multiStayRule.BestResults, allPetDetails, multiStayRule.ServiceId)
		}
	}

	// 应用peak date规则
	e.applyPeakDateRuleForAllServices(allPetDetails, serviceIdRule)
}

func selectBetterCalculateResult(current, best *do.CalculateDO) *do.CalculateDO {
	// 没有命中 pricing rule 直接返回原结果
	if current == nil || !current.IsHitPricingRule {
		return best
	}
	if best == nil {
		return current
	}

	// 默认选择最高价
	if best.AdjustedPrice.GreaterThan(current.AdjustedPrice) {
		return best
	}
	return current
}

// 生成宠物服务日期的唯一键
func generatePetServiceDateKey(petId int64, serviceId int64, serviceDate string) string {
	return fmt.Sprintf("%d_%d_%s", petId, serviceId, serviceDate)
}

// 从CalculateDO生成唯一键
func getCalculateKey(detail *do.CalculateDO) string {
	serviceDate := ""
	if detail.ServiceDate != nil {
		serviceDate = *detail.ServiceDate
	}
	return generatePetServiceDateKey(detail.PetId, detail.ServiceId, serviceDate)
}

func (e *PricingEngine) CalculateFormula(petDetails []*do.CalculateDO, symbol string) string {
	details := e.initializeResults(petDetails)
	serviceIdRule := groupRulesByServiceId(details, e.Rules)

	for _, serviceRule := range serviceIdRule {
		serviceId := serviceRule.ServiceId
		multiPet := serviceRule.MultiPetRuleRecord
		multiStay := serviceRule.MultiStayRuleRecord

		currentServiceDetails := lo.Filter(details, func(detail *do.CalculateDO, _ int) bool {
			return detail.ServiceId == serviceId
		})

		formula := fmt.Sprintf("%s%s", symbol, currentServiceDetails[0].ServicePrice.StringFixed(2))

		// multi pet, multi stay
		if e.ApplyBestOnly {
			// 计算多宠物规则的折扣
			multiPetResults := cloneResults(currentServiceDetails)
			e.multiPetRule.Apply(multiPetResults, multiPet)

			// 计算多住宿规则的折扣
			multiStayResults := cloneResults(currentServiceDetails)
			e.multiStayRule.Apply(multiStayResults, multiStay)

			// 比较总折扣
			petDiscount := calculatePriceDifference(multiPetResults, serviceId, multiPet.ID)
			stayDiscount := calculatePriceDifference(multiStayResults, serviceId, multiStay.ID)

			// 应用折扣更好的结果
			if petDiscount.GreaterThan(stayDiscount) {
				adjustedPrice := getAdjustedPrice(multiPetResults)
				formula = e.multiPetRule.AppendFormula(currentServiceDetails, multiPet, symbol, formula) + " = " + fmt.Sprintf("%s%s", symbol, adjustedPrice.StringFixed(2))
			} else {
				adjustedPrice := getAdjustedPrice(multiStayResults)
				formula = e.multiStayRule.AppendFormula(currentServiceDetails, multiStay, symbol, formula) + " = " + fmt.Sprintf("%s%s", symbol, adjustedPrice.StringFixed(2))
			}

			return formula
		} else {
			for _, ruleType := range e.ApplySequence {
				if ruleType == offeringpb.RuleType_MULTIPLE_PET {
					e.multiPetRule.Apply(currentServiceDetails, multiPet)
					formula = e.multiPetRule.AppendFormula(currentServiceDetails, multiPet, symbol, formula)
				} else if ruleType == offeringpb.RuleType_MULTIPLE_STAY {
					e.multiStayRule.Apply(currentServiceDetails, multiStay)
					formula = e.multiStayRule.AppendFormula(currentServiceDetails, multiStay, symbol, formula)
				}
			}

			// 获取最终价格
			adjustedPrice := getAdjustedPrice(currentServiceDetails)
			return formula + " = " + fmt.Sprintf("%s%s", symbol, adjustedPrice.StringFixed(2))
		}

	}

	return ""
}

func updateFinalResults(results []*do.CalculateDO, details []*do.CalculateDO) {
	for _, detail := range details {
		for _, result := range results {
			if result.PetId == detail.PetId &&
				result.ServiceId == detail.ServiceId &&
				result.ServiceDate == detail.ServiceDate {
				detail.AdjustedPrice = result.AdjustedPrice
				detail.IsHitPricingRule = result.IsHitPricingRule
				detail.UsedPricingRuleIds = result.UsedPricingRuleIds
				detail.PricingRuleIdPriceMap = result.PricingRuleIdPriceMap
			}
		}
	}
}

// updateFinalResultsForService 只更新特定serviceId的结果
func updateFinalResultsForService(results []*do.CalculateDO, details []*do.CalculateDO, serviceId int64) {
	for _, detail := range details {
		// 只处理指定serviceId的数据
		if detail.ServiceId != serviceId {
			continue
		}

		for _, result := range results {
			if result.PetId == detail.PetId &&
				result.ServiceId == detail.ServiceId &&
				result.ServiceDate == detail.ServiceDate {
				detail.AdjustedPrice = result.AdjustedPrice
				detail.IsHitPricingRule = result.IsHitPricingRule
				detail.UsedPricingRuleIds = result.UsedPricingRuleIds
				detail.PricingRuleIdPriceMap = result.PricingRuleIdPriceMap
				break
			}
		}
	}
}

func cloneResults(details []*do.CalculateDO) []*do.CalculateDO {
	results := make([]*do.CalculateDO, len(details))
	for i, petDetail := range details {
		results[i] = &do.CalculateDO{
			PetId:                 petDetail.PetId,
			ServiceId:             petDetail.ServiceId,
			ServicePrice:          petDetail.ServicePrice,
			LodgingUnitId:         petDetail.LodgingUnitId,
			ScopeTypePrice:        petDetail.ScopeTypePrice,
			ServiceDate:           petDetail.ServiceDate,
			IsHitPricingRule:      petDetail.IsHitPricingRule,
			UsedPricingRuleIds:    petDetail.UsedPricingRuleIds,
			ServiceItemType:       petDetail.ServiceItemType,
			AdjustedPrice:         petDetail.AdjustedPrice,
			PricingRuleIdPriceMap: cloneDecimalMap(petDetail.PricingRuleIdPriceMap),
		}
	}
	return results
}

func (e *PricingEngine) initializeResults(details []*do.CalculateDO) []*do.CalculateDO {
	results := make([]*do.CalculateDO, len(details))
	for i, petDetail := range details {
		results[i] = &do.CalculateDO{
			PetId:                 petDetail.PetId,
			ServiceId:             petDetail.ServiceId,
			ServicePrice:          petDetail.ServicePrice,
			LodgingUnitId:         petDetail.LodgingUnitId,
			ScopeTypePrice:        petDetail.ScopeTypePrice,
			ServiceDate:           petDetail.ServiceDate,
			IsHitPricingRule:      false,
			UsedPricingRuleIds:    make([]int64, 0),
			ServiceItemType:       petDetail.ServiceItemType,
			AdjustedPrice:         petDetail.ServicePrice.RoundBank(2),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		}
	}

	// sort by price (descending) and then by pet id
	sort.Slice(results, func(i, j int) bool {
		// If prices are different, sort by price descending
		if !results[i].ServicePrice.Equal(results[j].ServicePrice) {
			return results[i].ServicePrice.GreaterThan(results[j].ServicePrice)
		}

		// If prices are equal, sort by pet id descending
		return results[i].PetId < results[j].PetId
	})

	return results
}

// 计算当前 service 的总折扣
func calculatePriceDifference(results []*do.CalculateDO, serviceId int64, ruleId *int64) decimal.Decimal {
	adjustedPrice := decimal.Zero
	for _, result := range results {
		if result.ServiceId != serviceId || ruleId == nil {
			continue
		}
		value, ok := result.PricingRuleIdPriceMap[*ruleId]
		if ok {
			adjustedPrice = adjustedPrice.Add(value)
		}
	}

	return adjustedPrice
}

func cloneDecimalMap(original map[int64]decimal.Decimal) map[int64]decimal.Decimal {
	clone := make(map[int64]decimal.Decimal, len(original))

	for key, value := range original {
		clone[key] = value
	}

	return clone
}
