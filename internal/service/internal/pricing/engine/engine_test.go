package engine

import (
	"testing"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

const (
	symbol = "$"
)

func TestPricingEngine_CalculatePrice_NoRules(t *testing.T) {
	engine := NewPricingEngine([]do.PricingRuleRecordDO{}, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.To<PERSON>("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 1, len(results))
	assert.Equal(t, decimal.NewFromFloat32(100).RoundBank(2), results[0].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_NoRules(t *testing.T) {
	engine := NewPricingEngine([]do.PricingRuleRecordDO{}, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 = $100.00", formula)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_MultiPetRule_EachPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $10.00 = $90.00", formula)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_AdditionalPets(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(200).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
	assert.True(t, results[1].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_AdditionalPets_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(200).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
	assert.True(t, results[1].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_MultiPetRule_AdditionalPets(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $10.00 = $90.00", formula)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $20.00 = $80.00", formula)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultiPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultiPet_DifferentService(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_ApplySequence(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "($100.00 - $10.00) * (1 - 10%) = $81.00", formula)
}

func TestPricingEngine_CalculatePrice_PeakDateRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-02",
										},
									},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 1, len(results))
	assert.Equal(t, decimal.NewFromFloat32(110).RoundBank(2), results[0].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence_WithPeakDateRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-02",
										},
									},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

// TestPricingEngine_CalculatePrice_ApplyBestOnly_MultipleServiceIds_OnlyMultiPetRule
// 测试当 ApplyBestOnly = true 且有多个 serviceId (>2) 时，如果只存在 multiPetRule，
// allPetDetails 不会被重复更新的问题
func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultipleServiceIds_OnlyMultiPetRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
		// 注意：这里没有 multiStayRule，只有 multiPetRule
	}

	engine := NewPricingEngine(rules, true, nil) // ApplyBestOnly = true

	details := []*do.CalculateDO{
		// Service 100 - Pet 1
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 100 - Pet 2
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200 - Pet 1
		{
			PetId:           1,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 200 - Pet 2
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(150),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 300 - Pet 1
		{
			PetId:           1,
			ServiceId:       300,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		// Service 300 - Pet 2
		{
			PetId:           2,
			ServiceId:       300,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	// 验证结果
	assert.Equal(t, 6, len(results))

	// 由于有2只宠物，满足多宠物规则条件，每只宠物应该获得20元折扣
	// 多宠物规则应该只应用一次，影响所有宠物
	expectedPrices := []decimal.Decimal{
		decimal.NewFromFloat32(180), // Pet 1, Service 300: 200 - 20 = 180
		decimal.NewFromFloat32(180), // Pet 2, Service 300: 200 - 20 = 180
		decimal.NewFromFloat32(130), // Pet 1, Service 200: 150 - 20 = 130
		decimal.NewFromFloat32(130), // Pet 2, Service 200: 150 - 20 = 130
		decimal.NewFromFloat32(80),  // Pet 1, Service 100: 100 - 20 = 80
		decimal.NewFromFloat32(80),  // Pet 2, Service 100: 100 - 20 = 80
	}

	for i, result := range results {
		assert.True(t, expectedPrices[i].Equal(result.AdjustedPrice),
			"Result %d: expected %s, got %s", i, expectedPrices[i].String(), result.AdjustedPrice.String())
		assert.True(t, result.IsHitPricingRule, "Result %d should hit pricing rule", i)
		assert.Contains(t, result.UsedPricingRuleIds, int64(1), "Result %d should use rule ID 1", i)
		assert.True(t, decimal.NewFromFloat32(20).Equal(result.PricingRuleIdPriceMap[1]),
			"Result %d should have 20 discount", i)
	}
}
