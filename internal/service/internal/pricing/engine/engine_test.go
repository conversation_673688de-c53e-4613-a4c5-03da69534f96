package engine

import (
	"testing"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

const (
	symbol = "$"
)

func TestPricingEngine_CalculatePrice_NoRules(t *testing.T) {
	engine := NewPricingEngine([]do.PricingRuleRecordDO{}, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.To<PERSON>("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 1, len(results))
	assert.Equal(t, decimal.NewFromFloat32(100).RoundBank(2), results[0].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_NoRules(t *testing.T) {
	engine := NewPricingEngine([]do.PricingRuleRecordDO{}, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 = $100.00", formula)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_EachPet_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_MultiPetRule_EachPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $10.00 = $90.00", formula)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_AdditionalPets(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(200).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
	assert.True(t, results[1].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_MultiPetRule_AdditionalPets_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(200),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(200).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(90).RoundBank(2), results[1].AdjustedPrice)
	assert.False(t, results[0].IsHitPricingRule)
	assert.True(t, results[1].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_MultiPetRule_AdditionalPets(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_ADDITIONAL,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET})

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $10.00 = $90.00", formula)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 2, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_ApplyBestOnly(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "$100.00 - $20.00 = $80.00", formula)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultiPet(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplyBestOnly_MultiPet_DifferentService(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(20),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, true, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(80).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence_DifferentServiceId(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       200,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(81).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculateFormula_ApplySequence(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	formula := engine.CalculateFormula(details, symbol)

	assert.Equal(t, "($100.00 - $10.00) * (1 - 10%) = $81.00", formula)
}

func TestPricingEngine_CalculatePrice_PeakDateRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-02",
										},
									},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	engine := NewPricingEngine(rules, false, nil)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 1, len(results))
	assert.Equal(t, decimal.NewFromFloat32(110).RoundBank(2), results[0].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}

func TestPricingEngine_CalculatePrice_ApplySequence_WithPeakDateRule(t *testing.T) {
	rules := []do.PricingRuleRecordDO{
		{
			ID:                       proto.Int64(1),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_PET,
			RuleName:                 "Multiple Pets",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_PET_COUNT,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(2),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_MULTIPLE_STAY,
			RuleName:                 "Multiple Stays",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_STAY_LENGTH,
								Operator: utilsV2.Operator_OPERATOR_GE,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_NumberValue{NumberValue: float64(2)},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
							Value: float64(10),
						},
					},
				},
			},
		},
		{
			ID:                       proto.Int64(3),
			CompanyID:                0,
			RuleType:                 offeringModelsV2.RuleType_PEAK_DATE,
			RuleName:                 "Peak Date",
			IsActive:                 true,
			AllBoardingApplicable:    true,
			SelectedBoardingServices: nil,
			AllDaycareApplicable:     true,
			SelectedDaycareServices:  nil,
			RuleApplyType:            offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
			NeedInSameLodging:        false,
			RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
				ConditionGroups: []*offeringModelsV2.ConditionGroup{
					{
						Conditions: []*offeringModelsV2.Condition{
							{
								Type:     offeringModelsV2.ConditionType_DATE_RANGE,
								Operator: utilsV2.Operator_OPERATOR_BETWEEN,
								Value: &offeringModelsV2.GenericValue{
									Value: &offeringModelsV2.GenericValue_DateRange{
										DateRange: &utilsV2.StringDateRange{
											StartDate: "2025-03-01",
											EndDate:   "2025-03-02",
										},
									},
								}},
						},
						Effect: &offeringModelsV2.Effect{
							Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
							Value: float64(10),
						},
					},
				},
			},
		},
	}

	applySequence := []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY}
	engine := NewPricingEngine(rules, false, applySequence)

	details := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
		{
			PetId:           2,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat32(100),
			ServiceDate:     lo.ToPtr("2025-03-02"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
		},
	}

	results := engine.CalculatePrice(details)

	assert.Equal(t, 4, len(results))
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[0].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[1].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[2].AdjustedPrice)
	assert.Equal(t, decimal.NewFromFloat32(91).RoundBank(2), results[3].AdjustedPrice)
	assert.True(t, results[0].IsHitPricingRule)
}
