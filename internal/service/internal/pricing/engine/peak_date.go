package engine

import (
	"context"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

type PeakDateRule struct{}

func NewPeakDateRule() *PeakDateRule {
	return &PeakDateRule{}
}

func (r *PeakDateRule) Apply(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO) {
	if len(petDetails) == 0 || rule.RuleConfiguration == nil {
		return
	}

	// 按照服务日期分组宠物
	dateToPetDetails := make(map[string][]*do.CalculateDO)
	for _, petDetail := range petDetails {
		if petDetail.ServiceDate == nil {
			continue
		}
		date := *petDetail.ServiceDate
		dateToPetDetails[date] = append(dateToPetDetails[date], petDetail)
	}

	// 处理每个日期组
	for date, petDetailList := range dateToPetDetails {
		// 查找适用的条件组
		applicableGroup := findBestPeakDateGroup(rule, date)
		if applicableGroup == nil {
			continue
		}

		effect := applicableGroup.GetEffect()
		if effect == nil {
			continue
		}

		switch rule.RuleApplyType {
		case offeringpb.RuleApplyType_APPLY_TO_ALL_PETS:
			// 应用到第一只宠物（新逻辑：根据IsChargePerLodging决定分组方式）
			applyEffectToFirstPet(petDetailList, effect, *rule.ID, rule.IsChargePerLodging)
		case offeringpb.RuleApplyType_APPLY_TO_FIRST_PET:
			// 应用到第一只宠物（新逻辑：根据IsChargePerLodging决定分组方式）
			applyEffectToFirstPet(petDetailList, effect, *rule.ID, rule.IsChargePerLodging)
		case offeringpb.RuleApplyType_APPLY_TO_EACH, offeringpb.RuleApplyType_RULE_APPLY_TYPE_UNSPECIFIED:
			// 默认应用到每个宠物
			for _, petDetail := range petDetailList {
				applyEffect(petDetail, effect, *rule.ID)
			}
		default:
			// 非预期的应用类型
			zlog.Error(context.Background(),
				"unexpected effect apply type",
				zap.Int32("apply_type", int32(rule.RuleApplyType)),
				zap.Int64("rule_id", *rule.ID))
		}
	}
}

func findBestPeakDateGroup(rule do.PricingRuleRecordDO, dateStr string) *offeringpb.ConditionGroup {
	var bestGroup *offeringpb.ConditionGroup

	for i, group := range rule.RuleConfiguration.ConditionGroups {
		for _, condition := range group.Conditions {
			if condition.Type == offeringpb.ConditionType_DATE_RANGE && condition.Value.GetDateRange() != nil {
				dateRange := condition.Value.GetDateRange()
				if isDateInRange(dateStr, dateRange.StartDate, dateRange.EndDate) {
					// 找到匹配的条件组
					return rule.RuleConfiguration.ConditionGroups[i]
				}
			}
		}
	}

	return bestGroup
}

// applyEffectToFirstPet 根据IsChargePerLodging决定将效果应用到第一只宠物还是每个lodging的第一只宠物
func applyEffectToFirstPet(petDetails []*do.CalculateDO, effect *offeringpb.Effect, ruleId int64, isChargePerLodging bool) {
	if len(petDetails) == 0 {
		return
	}

	if !isChargePerLodging {
		// IsChargePerLodging = false: 将价格变化应用到第一只宠物
		applyEffectToSingleFirstPet(petDetails, effect, ruleId)
	} else {
		// IsChargePerLodging = true: 按lodging分组，每个lodging的第一只宠物应用价格变化
		applyEffectToFirstPetPerLodging(petDetails, effect, ruleId)
	}
}

// applyEffectToSingleFirstPet 将效果应用到第一只宠物
func applyEffectToSingleFirstPet(petDetails []*do.CalculateDO, effect *offeringpb.Effect, ruleId int64) {
	if len(petDetails) == 0 {
		return
	}

	// 计算所有宠物的总价
	totalOriginalPrice := decimal.Zero
	for _, petDetail := range petDetails {
		totalOriginalPrice = totalOriginalPrice.Add(petDetail.AdjustedPrice)
	}

	// 计算应用效果后的总价
	totalAdjustedPrice := calculatePrice(totalOriginalPrice, effect)

	// 计算总的价格变化
	totalPriceChange := totalAdjustedPrice.Sub(totalOriginalPrice)

	// 将全部价格变化应用到第一只宠物
	firstPet := petDetails[0]
	originalPrice := firstPet.AdjustedPrice

	// 应用价格变化
	firstPet.AdjustedPrice = firstPet.AdjustedPrice.Add(totalPriceChange).RoundBank(2)
	firstPet.IsHitPricingRule = true
	firstPet.UsedPricingRuleIds = append(firstPet.UsedPricingRuleIds, ruleId)
	firstPet.PricingRuleIdPriceMap[ruleId] = decimal.Decimal.Abs(originalPrice.Sub(firstPet.AdjustedPrice)).RoundBank(2)
}

// applyEffectToFirstPetPerLodging 按lodging分组，每个lodging的第一只宠物应用价格变化
func applyEffectToFirstPetPerLodging(petDetails []*do.CalculateDO, effect *offeringpb.Effect, ruleId int64) {
	if len(petDetails) == 0 {
		return
	}

	// 按lodging分组宠物
	lodgingGroups := make(map[int64][]*do.CalculateDO)

	for _, petDetail := range petDetails {
		if petDetail.LodgingUnitId != nil {
			lodgingId := *petDetail.LodgingUnitId
			lodgingGroups[lodgingId] = append(lodgingGroups[lodgingId], petDetail)
		}
	}

	// 处理每个lodging组的宠物
	for _, lodgingPets := range lodgingGroups {
		if len(lodgingPets) > 0 {
			// 计算该lodging组所有宠物的总价
			totalOriginalPrice := decimal.Zero
			for _, petDetail := range lodgingPets {
				totalOriginalPrice = totalOriginalPrice.Add(petDetail.AdjustedPrice)
			}

			// 计算应用效果后的总价
			totalAdjustedPrice := calculatePrice(totalOriginalPrice, effect)

			// 计算总的价格变化
			totalPriceChange := totalAdjustedPrice.Sub(totalOriginalPrice)

			// 将价格变化应用到该lodging的第一只宠物
			firstPet := lodgingPets[0]
			originalPrice := firstPet.AdjustedPrice

			firstPet.AdjustedPrice = firstPet.AdjustedPrice.Add(totalPriceChange).RoundBank(2)
			firstPet.IsHitPricingRule = true
			firstPet.UsedPricingRuleIds = append(firstPet.UsedPricingRuleIds, ruleId)
			firstPet.PricingRuleIdPriceMap[ruleId] = decimal.Decimal.Abs(originalPrice.Sub(firstPet.AdjustedPrice)).RoundBank(2)
		}
	}
}

func (r *PeakDateRule) AppendFormula(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO, symbol string, formula string) string {
	return formula
}
