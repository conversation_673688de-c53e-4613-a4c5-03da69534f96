package engine

import (
	"testing"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

func TestPeakDateRule_Apply_EmptyPetDetails(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - no panic, function should return without changes
	assert.Empty(t, petDetails)
}

func TestPeakDateRule_Apply_NilRuleConfiguration(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:   decimal.NewFromFloat(100),
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                proto.Int64(1),
		RuleType:          offeringModelsV2.RuleType_PEAK_DATE,
		RuleConfiguration: nil,
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - no changes should be made
	assert.Equal(t, decimal.NewFromFloat(100), petDetails[0].AdjustedPrice)
	assert.False(t, petDetails[0].IsHitPricingRule)
}

func TestPeakDateRule_Apply_NoMatchingDate(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat(100),
			ServiceDate:     lo.ToPtr("2025-03-10"), // Date outside the peak date range
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:   decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - no changes should be made
	assert.Equal(t, decimal.NewFromFloat(100), petDetails[0].AdjustedPrice)
	assert.False(t, petDetails[0].IsHitPricingRule)
}

func TestPeakDateRule_Apply_MatchingDate_ApplyToEach(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	expected1 := decimal.NewFromFloat(110)
	expected2 := decimal.NewFromFloat(160)

	assert.Equal(t, expected1, petDetails[0].AdjustedPrice)
	assert.Equal(t, expected2, petDetails[1].AdjustedPrice)
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.Equal(t, decimal.NewFromFloat(10), petDetails[0].PricingRuleIdPriceMap[1])
	assert.Equal(t, decimal.NewFromFloat(10), petDetails[1].PricingRuleIdPriceMap[1])
}

func TestPeakDateRule_Apply_MatchingDate_ApplyToFirstPet_IsChargePerLodgingFalse(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)),
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(2)),
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                 proto.Int64(1),
		RuleType:           offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType:      offeringModelsV2.RuleApplyType_APPLY_TO_ALL_PETS,
		IsChargePerLodging: false, // 不按lodging收费，应用到第一只宠物
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 20.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - 新逻辑：IsChargePerLodging=false，全部价格变化应用到第一只宠物
	// Total original price: 100 + 150 = 250
	// Total adjusted price: 250 + 20 = 270
	// 全部20元增加应用到第一只宠物

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromInt(120) // 100 + 20 (全部增加)
	expected2 := decimal.NewFromInt(150) // 150 + 0 (无变化)
	expectedChange1 := decimal.NewFromInt(20)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.False(t, petDetails[1].IsHitPricingRule) // 第二只宠物不受影响
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.Equal(t, 0, len(petDetails[1].PricingRuleIdPriceMap)) // 第二只宠物没有价格变化记录
}

func TestPeakDateRule_Apply_MatchingDate_PercentageDiscount(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
						Value: 20.0, // 20% discount
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// 100 - 20% = 80

	// 使用 decimal 直接比较
	expectedPrice := decimal.NewFromInt(80)
	expectedDiscount := decimal.NewFromInt(20)

	assert.True(t, expectedPrice.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedDiscount.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MultipleDates(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-02"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-10"), // Date outside the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	expected1 := decimal.NewFromFloat(110)
	expected2 := decimal.NewFromFloat(110)
	expected3 := decimal.NewFromFloat(100) // No change for date outside range

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, expected3.Equal(petDetails[2].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.False(t, petDetails[2].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[2].UsedPricingRuleIds, int64(1))
}

func TestPeakDateRule_Apply_EvenDistributionWithRemainder(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 3,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(200),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(200),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_ALL_PETS,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0, // 增加 10 元，不能被 3 整除
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// 总价格: 100 + 150 + 200 = 450
	// 总调整后价格: 450 + 10 = 460
	// 每个宠物平均增加: 10 / 3 = 3.33 (保留两位小数)
	// 余数: 10 - 3.33*3 = 0.01，应该加到第一个宠物上
	// 所以第一个宠物增加 3.34，其他宠物增加 3.33

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromFloat(103.34)
	expected2 := decimal.NewFromFloat(153.33)
	expected3 := decimal.NewFromFloat(203.33)
	expectedChange1 := decimal.NewFromFloat(3.34)
	expectedChange2 := decimal.NewFromFloat(3.33)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, expected3.Equal(petDetails[2].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.True(t, petDetails[2].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[2].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange2.Equal(petDetails[1].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange2.Equal(petDetails[2].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MultipleConditionGroups(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within first peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-04-01"), // Date within second peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-04-01",
										EndDate:   "2025-04-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 20.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	expected1 := decimal.NewFromFloat(110) // First date range: +10
	expected2 := decimal.NewFromFloat(120) // Second date range: +20

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	expectedChange1 := decimal.NewFromFloat(10)
	expectedChange2 := decimal.NewFromFloat(20)
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange2.Equal(petDetails[1].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MultipleDays_ApplyToAllPets(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_ALL_PETS,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-05-01",
										EndDate:   "2025-05-31",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 100.0, // $100 increase per day
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// For each day, the $100 increase should be distributed evenly between the two pets
	// So each pet should get a $50 increase per day
	// Pet 1: 100 + 50 = 150 for each day
	// Pet 2: 150 + 50 = 200 for each day

	// Check first pet's prices for all three days
	expectedPet1Price := decimal.NewFromFloat(150) // 100 + 50
	expectedPet2Price := decimal.NewFromFloat(200) // 150 + 50
	expectedChange := decimal.NewFromFloat(50)     // 100 / 2 pets

	// Check all days for pet 1
	for i := 0; i < 3; i++ {
		assert.True(t, expectedPet1Price.Equal(petDetails[i].AdjustedPrice), "Pet 1 day %d price incorrect", i+1)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 1 day %d should hit pricing rule", i+1)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 1 day %d should use rule ID 1", i+1)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 1 day %d price change incorrect", i+1)
	}

	// Check all days for pet 2
	for i := 3; i < 6; i++ {
		assert.True(t, expectedPet2Price.Equal(petDetails[i].AdjustedPrice), "Pet 2 day %d price incorrect", i-2)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 2 day %d should hit pricing rule", i-2)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 2 day %d should use rule ID 1", i-2)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 2 day %d price change incorrect", i-2)
	}

	// Total price increase should be $100 × 3 days = $300
	// Each pet gets $50 × 3 days = $150 increase in total
}

func TestPeakDateRule_Apply_MultipleDays_ApplyToEach(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-05-01",
										EndDate:   "2025-05-31",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 100.0, // $100 increase per pet per day
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// For APPLY_TO_EACH mode, each pet gets the full price increase for each day
	// Pet 1: 100 + 100 = 200 for each day
	// Pet 2: 150 + 100 = 250 for each day

	// Check first pet's prices for all three days
	expectedPet1Price := decimal.NewFromFloat(200) // 100 + 100
	expectedPet2Price := decimal.NewFromFloat(250) // 150 + 100
	expectedChange := decimal.NewFromFloat(100)    // Full $100 increase per pet

	// Check all days for pet 1
	for i := 0; i < 3; i++ {
		assert.True(t, expectedPet1Price.Equal(petDetails[i].AdjustedPrice), "Pet 1 day %d price incorrect", i+1)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 1 day %d should hit pricing rule", i+1)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 1 day %d should use rule ID 1", i+1)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 1 day %d price change incorrect", i+1)
	}

	// Check all days for pet 2
	for i := 3; i < 6; i++ {
		assert.True(t, expectedPet2Price.Equal(petDetails[i].AdjustedPrice), "Pet 2 day %d price incorrect", i-2)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 2 day %d should hit pricing rule", i-2)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 2 day %d should use rule ID 1", i-2)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 2 day %d price change incorrect", i-2)
	}

	// Total price increase should be $100 × 2 pets × 3 days = $600
	// Each pet gets $100 × 3 days = $300 increase in total
}
